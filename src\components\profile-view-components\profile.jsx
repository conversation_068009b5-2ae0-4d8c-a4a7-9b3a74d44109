'use client';
import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchProfilePageDetails } from '@/lib/features/employees/employeeSlice';
import { ProfileCard } from './profile-card';
import { MarkAttendance } from './mark-attendance';
import { CalendarCard } from './calendar-card';
import { ScrollArea } from '../ui/scroll-area';
import { LeaveCard } from './leave-card';
import { PlanCard } from './plan-card';
import { TaskCard } from './task-card';
import { NotificationCard } from './notification-card';

export function Profile() {
	const dispatch = useAppDispatch();
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const { userProfile } = useAppSelector((store) => store.employee);

	useEffect(() => {
		if (authenticatedUser?.userId) {
			dispatch(fetchProfilePageDetails(authenticatedUser.userId));
		}
	}, [dispatch, authenticatedUser?.userId]);

	return (
		<main className="w-full min-h-[calc(100vh-7rem)]">
			<section className="grid grid-cols-12 gap-4 h-full">
				{/* Left profile panel */}
				<article className="col-span-12 md:col-span-3 h-full">
					<ProfileCard userProfile={userProfile} />
				</article>

				{/* Right content (3-row layout) */}
				<article className="col-span-12 md:col-span-9 h-full grid grid-rows-[repeat(3,minmax(0,1fr))] gap-4">
					{/* Row 1: Attendance + Calendar */}
					<section className="grid grid-cols-2 gap-4 h-full">
						<div className="h-full">
							<MarkAttendance />
						</div>
						<div className="h-full">
							<CalendarCard />
						</div>
					</section>

					{/* Row 2: Leave + Tasks */}
					<section className="grid grid-cols-12 gap-4 h-full">
						<div className="col-span-12 md:col-span-4 h-full">
							<LeaveCard />
						</div>
						<div className="col-span-12 md:col-span-8 h-full">
							<TaskCard />
						</div>
					</section>

					{/* Row 3: Notifications + Plan */}
					<section className="grid grid-cols-12 gap-4 h-full">
						<div className="col-span-12 md:col-span-8 h-full">
							<NotificationCard />
						</div>
						<div className="col-span-12 md:col-span-4 h-full">
							<PlanCard />
						</div>
					</section>
				</article>
			</section>
		</main>
	);
}
