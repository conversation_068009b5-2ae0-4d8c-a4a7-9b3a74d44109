'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useWatch } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { earningsDetailsSchema } from '@/lib/schemas/employeeRegistrationSchema';
import { useEffect, useState, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchCurrencies } from '@/lib/features/location/locationSlice';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Check, ChevronsUpDown, Loader2 } from 'lucide-react';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import { cn, weeksInMonth } from '@/lib/utils';
import { RadioGroup, RadioGroupItem } from './ui/radio-group';
import { Switch } from './ui/switch';
import {
	fetchEmployeeDetails,
	resetEmployeeDetails,
	updateEmployeeEarningsDetails,
} from '@/lib/features/employees/employeeSlice';
import { toast } from 'sonner';
import { LoadingSubmitButton } from './loading-component';
import { useRouter, useSearchParams } from 'next/navigation';

// Rate field types
const RATE_FIELDS = {
	MONTHLY: 'monthly',
	HOURLY: 'hourly',
	DAILY: 'daily',
	WEEKLY: 'weekly',
	YEARLY: 'yearly',
};

export function EmployeeEarningsForm() {
	const dispatch = useAppDispatch();
	const { employeeDetails, isLoading } = useAppSelector(
		(store) => store.employee
	);
	const { currencies } = useAppSelector((store) => store.location);
	const { companyData } = useAppSelector((store) => store.companyDetails);
	const companyDetails = companyData?.companyDetails;
	const currency = companyDetails?.businessCountry[0]?.currency;
	const [lastModifiedField, setLastModifiedField] = useState(
		RATE_FIELDS.MONTHLY
	);
	const employeeId = useSearchParams().get('employeeId');
	const router = useRouter();

	const form = useForm({
		resolver: zodResolver(earningsDetailsSchema),
		defaultValues: {
			basicPay: {
				currency: currency ?? '',
				amount: 0,
			},
			paymentMode: 'bank',
			frequency: 'monthly',
			payBasis: 'monthly',
			dailyRate: 0,
			hourlyRate: 0,
			weeklyRate: 0,
			yearlyRate: 0,
			overtimeRate: 0,
			isSalaryAdvanceEligible: false,
			salaryAdvance: 0,
		},
	});

	const watchFields = useWatch({
		control: form.control,
		name: [
			'basicPay.amount',
			'hourlyRate',
			'dailyRate',
			'weeklyRate',
			'yearlyRate',
			'isSalaryAdvanceEligible',
			'paymentMode',
		],
	});
	const [
		basicPayAmount,
		hourlyRate,
		dailyRate,
		weeklyRate,
		yearlyRate,
		isSalaryAdvanceEligible,
		paymentMode,
	] = watchFields;
	useEffect(() => {
		if (employeeDetails?.employeeId) {
			dispatch(fetchEmployeeDetails(employeeDetails.employeeId));
		}
	}, [employeeDetails, dispatch]);

	useEffect(() => {
		if (paymentMode === 'bank') {
			form.setValue('bankName', '');
			form.setValue('accountNumber', '');
			form.setValue('accountHolderName', '');
			form.setValue('bankCode', '');
			form.setValue('swiftBIC', '');
			form.setValue('branchCode', '');
		} else {
			form.setValue('bankName', undefined);
			form.setValue('accountNumber', undefined);
			form.setValue('accountHolderName', undefined);
			form.setValue('bankCode', undefined);
			form.setValue('swiftBIC', undefined);
			form.setValue('branchCode', undefined);
		}
	}, [paymentMode, form]);

	// Parse number input and update form
	const parseNumberInput = useCallback(
		(field, value) => {
			const numValue = Number.parseFloat(value);
			// console.log(numValue);
			if (!isNaN(numValue)) {
				form.setValue(field, numValue, { shouldValidate: true });
			}
		},
		[form]
	);

	// Handle rate field change
	const handleRateChange = useCallback(
		(field, value) => {
			parseNumberInput(field, value);
			setLastModifiedField(
				field === 'basicPay.amount'
					? RATE_FIELDS.MONTHLY
					: field.replace('Rate', '')
			);
		},
		[parseNumberInput]
	);

	// Fetch currencies if needed
	useEffect(() => {
		if (currencies.length === 0) {
			dispatch(fetchCurrencies());
		}
	}, [currencies.length, dispatch]);

	// Calculate rates based on the last modified field
	const calculateRates = useCallback((amount, basis, companyDetails) => {
		if (!amount || !companyDetails) return null;

		const numAmount = Number(amount);
		const rates = {
			hourlyRate: 0,
			dailyRate: 0,
			weeklyRate: 0,
			monthlyRate: 0,
			yearlyRate: 0,
		};

		// Calculate based on the basis
		switch (basis) {
			case RATE_FIELDS.HOURLY:
				rates.hourlyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.dailyRate = Number.parseFloat(
					(rates.hourlyRate * companyDetails.hourlySchedule.total).toFixed(2)
				);
				rates.monthlyRate = Number.parseFloat(
					(rates.dailyRate * companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.weeklyRate = Number.parseFloat(
					(rates.monthlyRate / weeksInMonth).toFixed(2)
				);
				rates.yearlyRate = Number.parseFloat(
					(rates.monthlyRate * companyDetails.monthlySchedule.total).toFixed(2)
				);
				break;
			case RATE_FIELDS.DAILY:
				rates.dailyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.hourlyRate = Number.parseFloat(
					(rates.dailyRate / companyDetails.hourlySchedule.total).toFixed(2)
				);
				rates.monthlyRate = Number.parseFloat(
					(rates.dailyRate * companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.weeklyRate = Number.parseFloat(
					(rates.monthlyRate / weeksInMonth).toFixed(2)
				);
				rates.yearlyRate = Number.parseFloat(
					(rates.monthlyRate * companyDetails.monthlySchedule.total).toFixed(2)
				);
				break;
			case RATE_FIELDS.WEEKLY:
				rates.weeklyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.monthlyRate = Number.parseFloat(
					(rates.weeklyRate * weeksInMonth).toFixed(2)
				);
				rates.dailyRate = Number.parseFloat(
					(rates.monthlyRate / companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.hourlyRate = Number.parseFloat(
					(rates.dailyRate / companyDetails.hourlySchedule.total).toFixed(2)
				);
				rates.yearlyRate = Number.parseFloat(
					(rates.monthlyRate * companyDetails.monthlySchedule.total).toFixed(2)
				);
				break;
			case RATE_FIELDS.YEARLY:
				rates.yearlyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.monthlyRate = Number.parseFloat(
					(rates.yearlyRate / companyDetails.monthlySchedule.total).toFixed(2)
				);
				rates.weeklyRate = Number.parseFloat(
					(rates.monthlyRate / weeksInMonth).toFixed(2)
				);
				rates.dailyRate = Number.parseFloat(
					(rates.monthlyRate / companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.hourlyRate = Number.parseFloat(
					(rates.dailyRate / companyDetails.hourlySchedule.total).toFixed(2)
				);
				break;
			case RATE_FIELDS.MONTHLY:
			default:
				rates.monthlyRate = Number.parseFloat(numAmount.toFixed(2));
				rates.yearlyRate = Number.parseFloat(
					(rates.monthlyRate * companyDetails.monthlySchedule.total).toFixed(2)
				);
				rates.weeklyRate = Number.parseFloat(
					(rates.monthlyRate / weeksInMonth).toFixed(2)
				);
				rates.dailyRate = Number.parseFloat(
					(rates.monthlyRate / companyDetails.dailySchedule.total).toFixed(2)
				);
				rates.hourlyRate = Number.parseFloat(
					(rates.dailyRate / companyDetails.hourlySchedule.total).toFixed(2)
				);
				break;
		}

		return rates;
	}, []);

	// Update rates when a field changes
	useEffect(() => {
		if (!companyDetails) return;

		// Skip calculation if form is initializing
		if (
			Object.values(form.getValues()).every(
				(val) => val === 0 || val === undefined || val === ''
			)
		) {
			return;
		}

		// Get the value of the last modified field
		const fieldValues = {
			[RATE_FIELDS.MONTHLY]: basicPayAmount,
			[RATE_FIELDS.HOURLY]: hourlyRate,
			[RATE_FIELDS.DAILY]: dailyRate,
			[RATE_FIELDS.WEEKLY]: weeklyRate,
			[RATE_FIELDS.YEARLY]: yearlyRate,
		};

		const changedValue = fieldValues[lastModifiedField];
		const calculatedRates = calculateRates(
			changedValue,
			lastModifiedField,
			companyDetails
		);

		if (calculatedRates) {
			// Update all fields except the one that was modified
			if (lastModifiedField !== RATE_FIELDS.MONTHLY) {
				form.setValue(
					'basicPay.amount',
					Number.parseFloat(calculatedRates.monthlyRate.toFixed(2))
				);
			}
			if (lastModifiedField !== RATE_FIELDS.HOURLY) {
				form.setValue(
					'hourlyRate',
					Number.parseFloat(calculatedRates.hourlyRate)
				);
			}
			if (lastModifiedField !== RATE_FIELDS.DAILY) {
				form.setValue(
					'dailyRate',
					Number.parseFloat(calculatedRates.dailyRate)
				);
			}
			if (lastModifiedField !== RATE_FIELDS.WEEKLY) {
				form.setValue(
					'weeklyRate',
					Number.parseFloat(calculatedRates.weeklyRate)
				);
			}
			if (lastModifiedField !== RATE_FIELDS.YEARLY) {
				form.setValue(
					'yearlyRate',
					Number.parseFloat(calculatedRates.yearlyRate)
				);
			}
		}
	}, [
		basicPayAmount,
		hourlyRate,
		dailyRate,
		weeklyRate,
		yearlyRate,
		lastModifiedField,
		form,
		companyDetails,
		calculateRates,
	]);

	// Format date for input field
	const formatDateForInput = useCallback((date) => {
		if (!date) return '';
		if (typeof date === 'string') return date;
		const d = new Date(date);
		return isNaN(d.getTime()) ? '' : d.toISOString().split('T')[0];
	}, []);

	// Handle form submission
	function onSubmit(data) {
		// console.log(data);

		dispatch(
			updateEmployeeEarningsDetails({
				...data,
				employeeId: employeeDetails?._id || employeeDetails?.employeeId,
			})
		);

		if (dispatch.fulfilled) {
			dispatch(resetEmployeeDetails());
		}
		// FIXME: need to check whether the dispatch.fulfilled is accurate in this case or not and then delete it.
		dispatch(resetEmployeeDetails());

		if (employeeId) {
			router.push('/client-admin/hr-module/employees-list');
		}

		// if (employeeDetails?._id) {
		// } else {
		// 	toast.error('Employee ID not found, try editing the employee details.');
		// 	return;
		// }
	}

	// Render a rate field
	const renderRateField = (name, label, description) => (
		<FormField
			control={form.control}
			name={name}
			render={({ field }) => (
				<FormItem>
					<FormLabel>{label}</FormLabel>
					<FormControl>
						<Input
							type="number"
							value={field.value}
							onChange={(e) => handleRateChange(name, e.target.value)}
						/>
					</FormControl>
					{description && <FormDescription>{description}</FormDescription>}
				</FormItem>
			)}
		/>
	);

	/* Use this to debug form entries

	// useEffect(() => {
	// 	console.log(form.formState.errors);
	// }, [form.formState.errors]);

	// useEffect(() => {
	// 	                                                                               const subscription = form.watch((data) => {
	// 		console.log(data);
	// 	});
	// 	return () => subscription.unsubscribe();
	// }, [form]);
	
	*/

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Compensation Details</h3>
					<Separator />

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div className="space-y-4">
							<FormField
								control={form.control}
								name="basicPay.currency"
								render={({ field }) => {
									const currency = currencies?.find(
										(currency) => currency.currency === field.value
									);

									return (
										<FormItem className="col-span-8">
											<FormLabel>Basic Pay Currency</FormLabel>
											<Popover>
												<PopoverTrigger asChild>
													<FormControl>
														<Button
															variant="outline"
															role="combobox"
															className={cn(
																'w-full justify-between',
																!field.value && 'text-muted-foreground'
															)}
														>
															{currency
																? `${currency.currency} - ${currency.currencyName}`
																: 'Select currency'}
															<ChevronsUpDown className="opacity-50" />
														</Button>
													</FormControl>
												</PopoverTrigger>
												<PopoverContent className="w-full p-0">
													<Command>
														<CommandInput
															placeholder="Search Currency..."
															className="h-9"
														/>
														<CommandList>
															<CommandEmpty>No Currency found.</CommandEmpty>
															<CommandGroup>
																{currencies?.map((currency, index) => (
																	<CommandItem
																		value={currency.currency}
																		key={index + 5}
																		onSelect={() => {
																			form.setValue(
																				'basicPay.currency',
																				currency.currency
																			);
																		}}
																	>
																		{`${currency.currency} - ${currency.currencyName}`}
																		<Check
																			className={cn(
																				'ml-auto',
																				currency.currency === field.value
																					? 'opacity-100'
																					: 'opacity-0'
																			)}
																		/>
																	</CommandItem>
																))}
															</CommandGroup>
														</CommandList>
													</Command>
												</PopoverContent>
											</Popover>
											<FormMessage />
										</FormItem>
									);
								}}
							/>

							<FormField
								control={form.control}
								name="payBasis"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Pay Basis</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select pay basis" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="hourly">Hourly</SelectItem>
												<SelectItem value="daily">Daily</SelectItem>
												<SelectItem value="weekly">Weekly</SelectItem>
												<SelectItem value="monthly">Monthly</SelectItem>
											</SelectContent>
										</Select>
										<FormDescription>
											How the employee&apos;s pay is calculated
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="overtimeRate"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Overtime Rate (Hourly)</FormLabel>
										<FormControl>
											<Input
												type="number"
												placeholder="Enter overtime rate"
												value={field.value}
												onChange={(e) =>
													parseNumberInput('overtimeRate', e.target.value)
												}
												disabled={
													!employeeDetails?.employmentDetails?.overTimeEligible
												}
											/>
										</FormControl>
										<FormDescription>
											{employeeDetails?.employmentDetails?.overTimeEligible
												? 'Hourly rate paid for overtime work'
												: 'Overtime is not eligible for this employee'}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="space-y-4">
							<FormField
								control={form.control}
								name="basicPay.amount"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Basic Pay Amount (Monthly)</FormLabel>
										<FormControl>
											<Input
												type="number"
												placeholder="Enter amount"
												value={field.value}
												onChange={(e) =>
													handleRateChange('basicPay.amount', e.target.value)
												}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="frequency"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Payment Frequency</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select payment frequency" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="daily">Daily</SelectItem>
												<SelectItem value="weekly">Weekly</SelectItem>
												<SelectItem value="monthly">Monthly</SelectItem>
												<SelectItem value="bi-monthly">Bi-Monthly</SelectItem>
											</SelectContent>
										</Select>
										<FormDescription>
											How often the employee is paid
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>
				</div>

				<div className="space-y-4 mt-6 col-span-full">
					<h4 className="font-medium">Calculated Rates</h4>
					<Card>
						<CardContent className="pt-6">
							<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
								{renderRateField(
									'hourlyRate',
									'Hourly Rate',
									companyDetails?.hourlySchedule?.description
								)}
								{renderRateField(
									'dailyRate',
									'Daily Rate',
									companyDetails?.dailySchedule?.description
								)}
								{renderRateField(
									'weeklyRate',
									'Weekly Rate',
									'Monthly rate divided by number of weeks'
								)}
								{renderRateField(
									'yearlyRate',
									'Yearly Rate',
									companyDetails?.monthlySchedule?.description
								)}
							</div>
						</CardContent>
					</Card>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-medium">Payment Method</h3>
					<Separator />
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<FormField
							control={form.control}
							name="paymentMode"
							render={({ field }) => (
								<FormItem className="space-y-3">
									<FormLabel>Payment Mode</FormLabel>
									<FormControl>
										<RadioGroup
											onValueChange={field.onChange}
											defaultValue={field.value}
											className="flex flex-col space-y-1"
										>
											<FormItem className="flex items-center space-x-3 space-y-0">
												<FormControl>
													<RadioGroupItem value="cash/cheque" />
												</FormControl>
												<FormLabel className="font-normal">
													Cash / Cheque
												</FormLabel>
											</FormItem>
											<FormItem className="flex items-center space-x-3 space-y-0">
												<FormControl>
													<RadioGroupItem value="bank" />
												</FormControl>
												<FormLabel className="font-normal">Bank</FormLabel>
											</FormItem>
										</RadioGroup>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
					{form.watch('paymentMode') === 'bank' && (
						<div className="grid md:grid-cols-3 gap-6">
							<FormField
								control={form.control}
								name="bankName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Bank Name</FormLabel>
										<FormControl>
											<Input {...field} placeholder="Bank Name" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="accountNumber"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Account Number</FormLabel>
										<FormControl>
											<Input {...field} placeholder="Account Number" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="branchCode"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Branch Code</FormLabel>
										<FormControl>
											<Input {...field} placeholder="Branch Code" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="bankCode"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Bank Code</FormLabel>
										<FormControl>
											<Input {...field} placeholder="Bank Code" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="swiftBIC"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Swift BIC</FormLabel>
										<FormControl>
											<Input {...field} placeholder="Swift BIC" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="accountHolderName"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Account Holder Name</FormLabel>
										<FormControl>
											<Input {...field} placeholder="Account Holder Name" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					)}
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-medium">Additional Compensation</h3>
					<Separator />

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<FormField
							control={form.control}
							name="isSalaryAdvanceEligible"
							render={({ field }) => (
								<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
									<div className="space-y-0.5">
										<FormLabel>Eligible for Salary Advance</FormLabel>
										<FormDescription>
											Check this to enable salary advance
										</FormDescription>
									</div>
									<FormControl>
										<Switch
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
									</FormControl>
								</FormItem>
							)}
						/>
						{form.watch('isSalaryAdvanceEligible') && (
							<FormField
								control={form.control}
								name="salaryAdvance"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Salary Advance Amount</FormLabel>
										<FormControl>
											<Input
												type="number"
												placeholder="Enter salary advance amount"
												value={field.value}
												onChange={(e) =>
													parseNumberInput('salaryAdvance', e.target.value)
												}
												disabled={!isSalaryAdvanceEligible}
											/>
										</FormControl>
										<FormDescription>
											Optional salary advance amount
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}
					</div>
				</div>

				<div className="flex justify-end space-x-4">
					<LoadingSubmitButton
						isLoading={isLoading}
						buttonText={'Save and Next'}
						buttonLoadingText={'Saving...'}
					/>
				</div>
			</form>
		</Form>
	);
}
