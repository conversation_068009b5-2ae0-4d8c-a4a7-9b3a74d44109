'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DragDropContext } from '@hello-pangea/dnd';
import { cn } from '@/lib/utils';
import { KanbanColumn } from './KanbanColumn';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, X } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchTaskBoardOfProject,
	createTask,
	updateTaskPosition,
	createTaskGroup,
	updateTaskGroup,
} from '@/lib/features/tasks/tasksSlice';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { SimpleLoader } from '@/components/loading-component';

// Import schemas from root schema.js - these should match the schemas in schema.js
const createTaskGroupSchema = z.object({
	name: z.string().nonempty('Task group name is required'),
});

/**
 * KanbanBoard Component
 * Main kanban board container with horizontal scrolling groups
 */
export const KanbanBoard = ({ isGlassMode = false, className, ...props }) => {
	const dispatch = useAppDispatch();
	const { projectDetails: project } = useAppSelector((store) => store.projects);
	const { taskBoard, isLoading: isBoardLoading } = useAppSelector(
		(store) => store.tasks
	);
	const { companyData } = useAppSelector((store) => store.companyDetails);
	const projectId = project?.details?._id;
	// console.log(project, companyData);
	const [openDropdownId, setOpenDropdownId] = useState(null);
	const [isAddingGroup, setIsAddingGroup] = useState(false);

	// React Hook Form for group creation
	const groupForm = useForm({
		resolver: zodResolver(createTaskGroupSchema),
		defaultValues: {
			name: '',
			projectId: projectId,
			companyId: project?.companyId || '',
		},
	});

	// Drag and drop event handlers for @hello-pangea/dnd
	const handleDragEnd = (result) => {
		const { destination, source } = result;

		// If no destination, do nothing
		if (!destination) return;

		// If dropped in the same position, do nothing
		if (
			destination.droppableId === source.droppableId &&
			destination.index === source.index
		) {
			return;
		}

		const sourceGroupId = source.droppableId;
		const destinationGroupId = destination.droppableId;

		// Moving within the same group
		if (sourceGroupId === destinationGroupId) {
			const group = taskBoard?.groups.find((grp) => grp._id === sourceGroupId);
			console.log(group);
			if (!group) return;

			const newTasks = Array.from(group.tasks);
			const [movedTask] = newTasks.splice(source.index, 1);
			newTasks.splice(destination.index, 0, movedTask);

			// Update task position in backend for same group reordering
			dispatch(
				updateTaskPosition({
					projectId,
					positionData: {
						sourceIndex: source.index,
						destinationIndex: destination.index,
						destinationGroupId: destinationGroupId,
						sourceGroupId: sourceGroupId,
						taskId: movedTask._id,
					},
				})
			);
		} else {
			// Moving between different groups
			const sourceGroup = taskBoard.groups.find(
				(grp) => grp._id === sourceGroupId
			);
			const destinationGroup = taskBoard.groups.find(
				(grp) => grp._id === destinationGroupId
			);

			if (!sourceGroup || !destinationGroup) return;

			const sourceTasks = Array.from(sourceGroup.tasks);
			const destinationTasks = Array.from(destinationGroup.tasks);
			const [movedTask] = sourceTasks.splice(source.index, 1);
			destinationTasks.splice(destination.index, 0, movedTask);

			// Update task position in backend
			dispatch(
				updateTaskPosition({
					projectId,
					positionData: {
						sourceIndex: source.index,
						destinationIndex: destination.index,
						destinationGroupId: destinationGroupId,
						sourceGroupId: sourceGroupId,
						taskId: movedTask._id,
					},
				})
			);
		}
	};

	// if (isBoardLoading) {
	// 	return (
	// 		<div className="min-h-screen items-center justify-center">
	// 			<SimpleLoader />
	// 		</div>
	// 	);
	// }

	// if (isBoardLoading) {
	// 	return (
	// 		<div className="min-h-screen flex items-center justify-center">
	// 			<div className="text-white text-lg">Project not found</div>
	// 		</div>
	// 	);
	// }

	const onSubmit = async (data) => {
		console.log(data);
		const formData = {
			name: data.name,
			projectId,
			companyId: companyData?.companyDetails?._id,
		};

		const result = await dispatch(createTaskGroup(formData));

		if (createTaskGroup.fulfilled.match(result)) {
			setIsAddingGroup(false);
			groupForm.reset();
		}
	};

	// useEffect(() => {
	// 	console.log(groupForm.formState.errors);
	// }, [groupForm.formState.errors]);

	return (
		<DragDropContext onDragEnd={handleDragEnd}>
			<div className="flex gap-3 min-w-max">
				{taskBoard?.groups?.map((group) => (
					<div
						key={group._id}
						// className="animate-in slide-in-from-bottom duration-300"
						// style={{ animationDelay: `${index * 100}ms` }}
					>
						<KanbanColumn
							group={group}
							tasks={group.tasks}
							isGlassMode={isGlassMode}
							isDropdownOpen={openDropdownId === group._id}
							onDropdownOpenChange={(isOpen) =>
								setOpenDropdownId(isOpen ? group._id : null)
							}
						/>
					</div>
				))}

				{/* Add a Group */}
				<div className="w-[17rem] flex-shrink-0">
					<Card
						className={cn(
							// Conditional styling based on glass mode
							isGlassMode
								? 'bg-white/10 backdrop-blur-md border border-white/20'
								: 'border-gray-200',
							'shadow-sm p-3 transition-all duration-300'
						)}
						style={{
							backgroundColor: !isGlassMode
								? 'rgba(255, 255, 255, 0.9)'
								: undefined,
						}}
					>
						{isAddingGroup ? (
							<Form {...groupForm}>
								<form
									id="create-group-form"
									onSubmit={groupForm.handleSubmit(onSubmit)}
									className="space-y-2"
								>
									<FormField
										control={groupForm.control}
										name="name"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														{...field}
														placeholder="Enter group title..."
														className="h-8 text-sm"
														autoFocus
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<div className="flex gap-2">
										<Button
											type="submit"
											form="create-group-form"
											size="sm"
											disabled={!groupForm.watch('name')?.trim()}
											className="h-7 px-3 text-xs"
										>
											Add Group
										</Button>
										<Button
											type="button"
											variant="ghost"
											size="sm"
											onClick={() => {
												setIsAddingGroup(false);
												groupForm.reset();
											}}
											className="h-7 px-2 text-xs"
										>
											<X className="h-3 w-3" />
										</Button>
									</div>
								</form>
							</Form>
						) : (
							<Button
								variant="ghost"
								onClick={() => setIsAddingGroup(true)}
								className={cn(
									'w-full justify-start text-left h-8',
									isGlassMode
										? 'text-white/70 hover:text-white hover:bg-white/20'
										: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
								)}
							>
								<Plus className="h-4 w-4 mr-2" />
								Add a group
							</Button>
						)}
					</Card>
				</div>
			</div>
		</DragDropContext>
	);
};
