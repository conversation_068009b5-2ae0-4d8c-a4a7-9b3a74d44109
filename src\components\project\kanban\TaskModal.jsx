'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';

// Import custom components
import {
	TaskHeader,
	TaskTitle,
	TaskControls,
	TaskDescription,
	TaskAttachments,
	TaskComments,
	AttachmentPreviewModal,
} from './components';

// Import custom hooks
import { useTaskForms, useTaskHandlers, useTaskData } from './hooks';

// Import Redux actions
import { useAppDispatch } from '@/lib/hooks';
import { updateTask } from '@/lib/features/tasks/tasksSlice';

/**
 * TaskModal Component
 * Modal for adding/editing tasks with react-hook-form and Zod validation
 * Now refactored into separate components and custom hooks for better maintainability
 */
export const TaskModal = ({ isOpen, onClose, taskId = null, onSave }) => {
	const isEditMode = !!taskId;
	const [previewAttachment, setPreviewAttachment] = useState(null);
	const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
	const dispatch = useAppDispatch();

	// Use custom hooks for better organization
	const { taskForm, commentForm, descriptionForm, updateFormsWithTaskData } =
		useTaskForms(taskId, isEditMode);

	const {
		handleUpdateTask,
		handleAddComment,
		handleUpdateDescription,
		handleUpdateDueDate,
	} = useTaskHandlers(taskId, commentForm, onSave, onClose);

	const {
		task,
		isLoading,
		getImageAttachments,
		getCoverImage,
		handleRemoveCoverImage,
		handleSetCoverImage,
	} = useTaskData(taskId, taskForm, updateFormsWithTaskData);

	// Attachment handlers
	const handleAttachmentPreview = (attachment) => {
		setPreviewAttachment(attachment);
		setIsPreviewModalOpen(true);
	};

	const handleAttachmentDownload = (attachment) => {
		const link = document.createElement('a');
		link.href = attachment.url;
		link.download = attachment.public_id || attachment.name || 'download';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	const handleAttachmentUpload = async (files) => {
		try {
			// Create attachment objects (this would typically involve uploading to cloud storage first)
			const newAttachments = files.map((file) => ({
				_id: Date.now() + Math.random(), // Temporary ID
				url: URL.createObjectURL(file), // Temporary URL for preview
				public_id: file.name,
				name: file.name,
				resource_type: file.type.startsWith('image/') ? 'image' : 'raw',
				bytes: file.size,
				type: file.type,
			}));

			// Get current attachments
			const currentMedia = task?.media || [];
			const updatedMedia = [...currentMedia, ...newAttachments];

			// Check if any new attachment is an image and set first image as cover if no cover exists
			let coverImage = taskForm.watch('coverImage');
			if (!coverImage) {
				const firstImage = newAttachments.find(
					(att) => att.resource_type === 'image'
				);
				if (firstImage) {
					coverImage = firstImage.public_id; // Use original name as cover
					taskForm.setValue('coverImage', coverImage);
				}
			}

			// Update task with new media and potentially new cover image
			const updateData = {
				media: updatedMedia,
				taskId: task._id,
			};

			if (coverImage && !task.coverImage) {
				updateData.coverImage = coverImage;
			}

			await dispatch(
				updateTask({
					projectId: task.projectId,
					taskData: updateData,
				})
			);
		} catch (error) {
			console.error('Error uploading attachments:', error);
		}
	};

	const handleAttachmentDelete = async (attachment) => {
		try {
			// Remove attachment from media array
			const currentMedia = task?.media || [];
			const updatedMedia = currentMedia.filter(
				(att) => att._id !== attachment._id
			);

			// Check if deleted attachment was the cover image
			let updateData = {
				media: updatedMedia,
				taskId: task._id,
			};

			const currentCover = taskForm.watch('coverImage');
			if (
				currentCover === attachment.public_id ||
				currentCover === attachment.url
			) {
				// Find next available image to set as cover
				const nextImage = updatedMedia.find(
					(att) => att.resource_type === 'image'
				);
				const newCover = nextImage ? nextImage.public_id : '';

				updateData.coverImage = newCover;
				taskForm.setValue('coverImage', newCover);
			}

			await dispatch(
				updateTask({
					projectId: task.projectId,
					taskData: updateData,
				})
			);
		} catch (error) {
			console.error('Error deleting attachment:', error);
		}
	};

	const handlePreviewModalClose = () => {
		setIsPreviewModalOpen(false);
		setPreviewAttachment(null);
	};

	return (
		<>
			<Dialog open={isOpen} onOpenChange={onClose}>
				<DialogContent
					hideClose={true}
					className="min-w-[64rem] h-[90vh] overflow-hidden p-0"
				>
					<Form {...taskForm}>
						<form onSubmit={taskForm.handleSubmit(handleUpdateTask)}>
							{/* Task Header Component */}
							<TaskHeader
								coverImage={getCoverImage()}
								onRemoveCoverImage={handleRemoveCoverImage}
								taskForm={taskForm}
								onClose={onClose}
							/>

							{/* Main Content - Two Columns */}
							<div className="flex h-[calc(90vh-64px)]">
								{/* Left Section - Larger */}
								<div className="flex-1 p-6 pl-3 pr-3 overflow-y-auto">
									{/* Task Title Component */}
									<TaskTitle taskForm={taskForm} />

									{/* Task Controls Component */}
									<TaskControls
										taskForm={taskForm}
										onUpdateDueDate={handleUpdateDueDate}
									/>

									{/* Task Description Component */}
									<TaskDescription
										descriptionForm={descriptionForm}
										onUpdateDescription={handleUpdateDescription}
									/>

									{/* Task Attachments Component */}
									<TaskAttachments
										attachments={getImageAttachments()}
										coverImageUrl={taskForm.watch('coverImage')}
										onSetCoverImage={handleSetCoverImage}
										onRemoveCoverImage={handleRemoveCoverImage}
										onAttachmentUpload={handleAttachmentUpload}
										onAttachmentPreview={handleAttachmentPreview}
										onAttachmentDownload={handleAttachmentDownload}
										onAttachmentDelete={handleAttachmentDelete}
									/>
								</div>

								{/* Task Comments Component */}
								<TaskComments
									task={task}
									commentForm={commentForm}
									onAddComment={handleAddComment}
								/>
							</div>
						</form>
					</Form>
				</DialogContent>
			</Dialog>

			{/* Attachment Preview Modal */}
			<AttachmentPreviewModal
				isOpen={isPreviewModalOpen}
				onClose={handlePreviewModalClose}
				attachment={previewAttachment}
				onDownload={handleAttachmentDownload}
			/>
		</>
	);
};
