'use client';

import React from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';

// Import custom components
import {
	TaskHeader,
	TaskTitle,
	TaskDescription,
	TaskAttachments,
	TaskComments,
} from './components';

// Import custom hooks
import { useTaskForms, useTaskHandlers, useTaskData } from './hooks';

/**
 * TaskModal Component
 * Modal for adding/editing tasks with react-hook-form and Zod validation
 * Now refactored into separate components and custom hooks for better maintainability
 */
export const TaskModal = ({ isOpen, onClose, taskId = null, onSave }) => {
	const isEditMode = !!taskId;

	// Use custom hooks for better organization
	const { taskForm, commentForm, descriptionForm, updateFormsWithTaskData } =
		useTaskForms(taskId, isEditMode);

	const { handleUpdateTask, handleAddComment, handleUpdateDescription } =
		useTaskHandlers(taskId, commentForm, onSave, onClose);

	const {
		task,
		isLoading,
		getImageAttachments,
		getCoverImage,
		handleRemoveCoverImage,
		handleSetCoverImage,
	} = useTaskData(taskId, taskForm, updateFormsWithTaskData);

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent
				hideClose={true}
				className="max-w-5xl max-h-[90vh] overflow-hidden p-0"
			>
				<Form {...taskForm}>
					<form onSubmit={taskForm.handleSubmit(handleUpdateTask)}>
						{/* Task Header Component */}
						<TaskHeader
							coverImage={getCoverImage()}
							onRemoveCoverImage={handleRemoveCoverImage}
							taskForm={taskForm}
							onClose={onClose}
						/>

						{/* Main Content - Two Columns */}
						<div className="flex h-[calc(90vh-200px)]">
							{/* Left Section - Larger */}
							<div className="flex-1 p-6 pr-3 overflow-y-auto">
								{/* Task Title Component */}
								<TaskTitle taskForm={taskForm} />

								{/* Task Description Component */}
								<TaskDescription
									descriptionForm={descriptionForm}
									onUpdateDescription={handleUpdateDescription}
								/>

								{/* Task Attachments Component */}
								<TaskAttachments
									attachments={getImageAttachments()}
									coverImageUrl={taskForm.watch('coverImage')}
									onSetCoverImage={handleSetCoverImage}
									onRemoveCoverImage={handleRemoveCoverImage}
								/>
							</div>

							{/* Task Comments Component */}
							<TaskComments
								task={task}
								commentForm={commentForm}
								onAddComment={handleAddComment}
							/>
						</div>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
};
