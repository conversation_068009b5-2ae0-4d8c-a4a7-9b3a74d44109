'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';

// Import custom components
import {
	TaskHeader,
	TaskTitle,
	TaskControls,
	TaskDescription,
	TaskAttachments,
	TaskComments,
	AttachmentPreviewModal,
} from './components';

// Import custom hooks
import { useTaskForms, useTaskHandlers, useTaskData } from './hooks';

/**
 * TaskModal Component
 * Modal for adding/editing tasks with react-hook-form and Zod validation
 * Now refactored into separate components and custom hooks for better maintainability
 */
export const TaskModal = ({ isOpen, onClose, taskId = null, onSave }) => {
	const isEditMode = !!taskId;
	const [previewAttachment, setPreviewAttachment] = useState(null);
	const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

	// Use custom hooks for better organization
	const { taskForm, commentForm, descriptionForm, updateFormsWithTaskData } =
		useTaskForms(taskId, isEditMode);

	const {
		handleUpdateTask,
		handleAddComment,
		handleUpdateDescription,
		handleUpdateDueDate,
	} = useTaskHandlers(taskId, commentForm, onSave, onClose);

	const {
		task,
		isLoading,
		getImageAttachments,
		getCoverImage,
		handleRemoveCoverImage,
		handleSetCoverImage,
	} = useTaskData(taskId, taskForm, updateFormsWithTaskData);

	// Attachment handlers
	const handleAttachmentPreview = (attachment) => {
		setPreviewAttachment(attachment);
		setIsPreviewModalOpen(true);
	};

	const handleAttachmentDownload = (attachment) => {
		const link = document.createElement('a');
		link.href = attachment.url;
		link.download = attachment.public_id || attachment.name || 'download';
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	const handleAttachmentUpload = async (files) => {
		// TODO: Implement file upload logic
		console.log('Uploading files:', files);
		// This would typically call an API to upload files
	};

	const handleAttachmentDelete = async (attachment) => {
		// TODO: Implement file deletion logic
		console.log('Deleting attachment:', attachment);
		// This would typically call an API to delete the attachment
	};

	const handlePreviewModalClose = () => {
		setIsPreviewModalOpen(false);
		setPreviewAttachment(null);
	};

	return (
		<>
			<Dialog open={isOpen} onOpenChange={onClose}>
				<DialogContent
					hideClose={true}
					className="min-w-[64rem] h-[90vh] overflow-hidden p-0"
				>
					<Form {...taskForm}>
						<form onSubmit={taskForm.handleSubmit(handleUpdateTask)}>
							{/* Task Header Component */}
							<TaskHeader
								coverImage={getCoverImage()}
								onRemoveCoverImage={handleRemoveCoverImage}
								onClose={onClose}
							/>

							{/* Main Content - Two Columns */}
							<div className="flex h-[calc(90vh-0px)]">
								{/* Left Section - Larger */}
								<div className="flex-1 p-6 pl-3 pr-3 overflow-y-auto">
									{/* Task Title Component */}
									<TaskTitle taskForm={taskForm} />

									{/* Task Controls Component */}
									<TaskControls
										taskForm={taskForm}
										onUpdateDueDate={handleUpdateDueDate}
									/>

									{/* Task Description Component */}
									<TaskDescription
										descriptionForm={descriptionForm}
										onUpdateDescription={handleUpdateDescription}
									/>

									{/* Task Attachments Component */}
									<TaskAttachments
										attachments={getImageAttachments()}
										coverImageUrl={taskForm.watch('coverImage')}
										onSetCoverImage={handleSetCoverImage}
										onRemoveCoverImage={handleRemoveCoverImage}
										onAttachmentUpload={handleAttachmentUpload}
										onAttachmentPreview={handleAttachmentPreview}
										onAttachmentDownload={handleAttachmentDownload}
										onAttachmentDelete={handleAttachmentDelete}
									/>
								</div>

								{/* Task Comments Component */}
								<TaskComments
									task={task}
									commentForm={commentForm}
									onAddComment={handleAddComment}
								/>
							</div>
						</form>
					</Form>
				</DialogContent>
			</Dialog>

			{/* Attachment Preview Modal */}
			<AttachmentPreviewModal
				isOpen={isPreviewModalOpen}
				onClose={handlePreviewModalClose}
				attachment={previewAttachment}
				onDownload={handleAttachmentDownload}
			/>
		</>
	);
};
