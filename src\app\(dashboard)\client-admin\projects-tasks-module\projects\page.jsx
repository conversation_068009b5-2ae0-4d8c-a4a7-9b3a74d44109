'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';

// Import new project components
import {
	ProjectCard,
	CreateProjectCard,
	CreateEditProjectDialog,
	ProjectSummaryDialog,
	sampleProjects,
} from '@/components/project';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchProjects } from '@/lib/features/projects/projectsSlice';

export default function ProjectsKanbanPage() {
	const { projects } = useAppSelector((store) => store.projects);
	const dispatch = useAppDispatch();
	const router = useRouter();
	const [selectedProject, setSelectedProject] = useState(null);
	const [summaryDialogOpen, setSummaryDialogOpen] = useState(false);
	const [editDialogOpen, setEditDialogOpen] = useState(false);
	const [editingProject, setEditingProject] = useState(null);

	const handleProjectClick = (project) => {
		// Navigate to the project's kanban board
		router.push(`/client-admin/projects-tasks-module/projects/${project._id}`);
	};

	const handleEditProject = (project) => {
		setEditingProject(project);
		setEditDialogOpen(true);
	};

	const handleViewSummary = (project) => {
		setSelectedProject(project);
		setSummaryDialogOpen(true);
	};

	const handleDeleteProject = (project) => {
		if (confirm(`Are you sure you want to delete "${project.title}"?`)) {
			// setProjects(projects.filter((p) => p.id !== project.id));
			console.log('Deleted project:', project);
		}
	};

	useEffect(() => {
		dispatch(fetchProjects());
	}, [dispatch]);

	return (
		<div className="flex w-full flex-col bg-background">
			<div className="flex flex-col">
				<main className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
					{/* Header */}
					<div className="flex items-center justify-between">
						<div>
							<h1 className="text-3xl font-bold tracking-tight">Projects</h1>
							<p className="text-muted-foreground">
								Manage and track your projects efficiently
							</p>
						</div>
						<CreateEditProjectDialog>
							<Button className="gap-2">
								<Plus className="h-4 w-4" />
								New Project
							</Button>
						</CreateEditProjectDialog>
					</div>

					{/* Projects Grid - Updated for 16:9 aspect ratio cards */}
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
						{/* Create New Project Card */}
						<CreateEditProjectDialog>
							<div>
								<CreateProjectCard />
							</div>
						</CreateEditProjectDialog>

						{/* Project Cards */}
						{projects.map((project) => (
							<ProjectCard
								key={project._id}
								project={project}
								onClick={handleProjectClick}
								onEdit={handleEditProject}
								onDelete={handleDeleteProject}
								onViewSummary={handleViewSummary}
							/>
						))}
					</div>

					{/* Project Summary Dialog */}
					<ProjectSummaryDialog
						project={selectedProject}
						open={summaryDialogOpen}
						onOpenChange={setSummaryDialogOpen}
					/>

					{/* Edit Project Dialog */}
					<CreateEditProjectDialog
						project={editingProject}
						open={editDialogOpen}
						onOpenChange={setEditDialogOpen}
					/>
				</main>
			</div>
		</div>
	);
}
