'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { X, User, Trash2, Flag, CheckCircle } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateTask } from '@/lib/features/tasks/tasksSlice';
import Image from 'next/image';

/**
 * TaskHeader Component
 * Renders the header section of the task modal with cover image, assignee selection, and close button
 */
export const TaskHeader = ({
	coverImage,
	onRemoveCoverImage,
	taskForm,
	onClose,
}) => {
	const { projectDetails } = useAppSelector((store) => store.projects);
	const { taskDetails } = useAppSelector((store) => store.tasks);
	const dispatch = useAppDispatch();
	const onSelectValueChange = (value) => {
		dispatch(updateTask({ assignedTo: value, taskId: taskDetails._id }));
	};

	const onStatusChange = (value) => {
		dispatch(updateTask({ status: value, taskId: taskDetails._id }));
	};

	const onPriorityChange = (value) => {
		dispatch(updateTask({ priority: value, taskId: taskDetails._id }));
	};

	const getPriorityColor = (priority) => {
		switch (priority) {
			case 'high':
				return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30';
			case 'medium':
				return 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/30';
			case 'low':
				return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30';
			default:
				return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/30';
		}
	};

	const getStatusColor = (status) => {
		switch (status) {
			case 'completed':
				return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/30';
			case 'in-progress':
				return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/30';
			case 'pending':
				return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/30';
			case 'cancelled':
				return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/30';
			case 'overdue':
				return 'text-orange-600 bg-orange-50 dark:text-orange-400 dark:bg-orange-900/30';
			default:
				return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/30';
		}
	};
	return (
		<div className="relative">
			{/* Cover Image */}
			{coverImage && (
				<div className="relative h-32 bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
					<Image
						src={coverImage.url}
						fill
						alt="Task cover"
						className="object-contain"
					/>
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={onRemoveCoverImage}
						className="absolute bottom-2 left-2 h-7 px-2 text-xs bg-black/50 text-white hover:bg-black/70"
					>
						<Trash2 className="h-3 w-3 mr-1" />
						Remove Cover
					</Button>
				</div>
			)}

			{/* Header with Controls */}
			<div className="p-4 border-b border-gray-200 dark:border-gray-700 space-y-4">
				{/* Top Row - Close Button */}
				<div className="flex justify-end">
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={onClose}
						className="h-8 w-8 p-0"
					>
						<X className="h-4 w-4" />
					</Button>
				</div>

				{/* Controls Row */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					{/* Assignee */}
					<div className="flex items-center gap-2">
						<User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
						<FormField
							control={taskForm.control}
							name="assignedTo"
							render={({ field }) => (
								<FormItem className="flex-1">
									<Select
										onValueChange={onSelectValueChange}
										value={field.value}
									>
										<FormControl>
											<SelectTrigger className="h-8">
												<SelectValue placeholder="Assignee" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{projectDetails?.assignedEmployees.map((assignee) => (
												<SelectItem
													key={assignee.userId}
													value={assignee.userId}
												>
													<div className="flex items-center gap-2">
														<Avatar className="h-5 w-5">
															<AvatarImage src={assignee.profilePhoto} />
															<AvatarFallback className="text-xs">
																{assignee.name
																	.split(' ')
																	.map((n) => n[0])
																	.join('')}
															</AvatarFallback>
														</Avatar>
														<span className="text-sm">{assignee.name}</span>
													</div>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{/* Status */}
					<div className="flex items-center gap-2">
						<CheckCircle className="h-4 w-4 text-gray-500 dark:text-gray-400" />
						<FormField
							control={taskForm.control}
							name="status"
							render={({ field }) => (
								<FormItem className="flex-1">
									<Select
										onValueChange={onStatusChange}
										value={taskDetails?.status || field.value}
									>
										<FormControl>
											<SelectTrigger
												className={`h-8 ${getStatusColor(taskDetails?.status || field.value)}`}
											>
												<SelectValue placeholder="Status" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{[
												'pending',
												'in-progress',
												'completed',
												'cancelled',
												'overdue',
											].map((status) => (
												<SelectItem key={status} value={status}>
													<span className="capitalize">
														{status.replace('-', ' ')}
													</span>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{/* Priority */}
					<div className="flex items-center gap-2">
						<Flag className="h-4 w-4 text-gray-500 dark:text-gray-400" />
						<FormField
							control={taskForm.control}
							name="priority"
							render={({ field }) => (
								<FormItem className="flex-1">
									<Select
										onValueChange={onPriorityChange}
										value={taskDetails?.priority || field.value}
									>
										<FormControl>
											<SelectTrigger
												className={`h-8 ${getPriorityColor(taskDetails?.priority || field.value)}`}
											>
												<SelectValue placeholder="Priority" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{['low', 'medium', 'high'].map((priority) => (
												<SelectItem key={priority} value={priority}>
													<span className="capitalize">{priority}</span>
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				</div>
			</div>
		</div>
	);
};
