'use client';

import React, { useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { X, User, Trash2 } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchEmployeesToAssign,
	updateTask,
} from '@/lib/features/tasks/tasksSlice';
import Image from 'next/image';

// Sample assignees data (in real app, this would come from API)
const sampleAssignees = [
	{ id: 1, name: '<PERSON>', email: '<EMAIL>', avatar: null },
	{ id: 2, name: '<PERSON>', email: '<EMAIL>', avatar: null },
	{ id: 3, name: '<PERSON>', email: '<EMAIL>', avatar: null },
	{ id: 4, name: '<PERSON>', email: '<EMAIL>', avatar: null },
];

/**
 * TaskHeader Component
 * Renders the header section of the task modal with cover image, assignee selection, and close button
 */
export const TaskHeader = ({
	coverImage,
	onRemoveCoverImage,
	taskForm,
	onClose,
}) => {
	const { projectDetails } = useAppSelector((store) => store.projects);
	const { taskDetails } = useAppSelector((store) => store.tasks);
	const dispatch = useAppDispatch();
	const onSelectValueChange = (value) => {
		dispatch(updateTask({ assignedTo: value, taskId: taskDetails._id }));
	};
	return (
		<div className="relative">
			{/* Cover Image */}
			{coverImage && (
				<div className="relative h-32 bg-gray-100 flex items-center justify-center">
					<Image
						src={coverImage.url}
						fill
						alt="Task cover"
						className="object-contain"
					/>
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={onRemoveCoverImage}
						className="absolute bottom-2 left-2 h-7 px-2 text-xs bg-black/50 text-white hover:bg-black/70"
					>
						<Trash2 className="h-3 w-3 mr-1" />
						Remove Cover
					</Button>
				</div>
			)}

			{/* Header with Close and Assignee */}
			<div className="flex items-center justify-between p-4 border-b">
				<div className="flex items-center gap-3">
					<User className="h-5 w-5 text-gray-500" />
					<FormField
						control={taskForm.control}
						name="assignedTo"
						render={({ field }) => (
							<FormItem>
								<Select onValueChange={onSelectValueChange} value={field.value}>
									<FormControl>
										<SelectTrigger className="w-48">
											<SelectValue placeholder="Select assignee" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{projectDetails?.assignedEmployees.map((assignee) => (
											<SelectItem key={assignee.userId} value={assignee.userId}>
												<div className="flex items-center gap-2">
													<Avatar className="h-6 w-6">
														<AvatarImage src={assignee.profilePhoto} />
														<AvatarFallback className="text-xs">
															{assignee.name
																.split(' ')
																.map((n) => n[0])
																.join('')}
														</AvatarFallback>
													</Avatar>
													<span>{assignee.name}</span>
												</div>
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormDescription>
									Employee Assigned for this task
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={onClose}
					className="h-8 w-8 p-0"
				>
					<X className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
};
