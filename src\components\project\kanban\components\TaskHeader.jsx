'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { X, Trash2 } from 'lucide-react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

/**
 * TaskHeader Component
 * Renders the header section of the task modal with cover image, assignee selection, and close button
 */
export const TaskHeader = ({ coverImage, onRemoveCoverImage, onClose }) => {
	return (
		<div className="relative">
			<div
				className={cn(
					'relative',
					coverImage ? 'h-32' : 'h-16',
					'border-b border-gray-200 dark:border-gray-700'
				)}
			>
				{coverImage && (
					<>
						<div className="absolute inset-0 bg-gray-100 dark:bg-gray-800">
							<Image
								src={coverImage.url}
								fill
								alt="Task cover"
								className="object-contain"
							/>
						</div>
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={onRemoveCoverImage}
							className="absolute bottom-2 right-2 h-7 px-2 text-xs bg-black/50 text-white hover:bg-black/70"
						>
							<Trash2 className="h-3 w-3 mr-1" />
							Remove Cover
						</Button>
					</>
				)}
				<div className="absolute top-4 right-4">
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={onClose}
						className="h-8 w-8 p-0"
					>
						<X className="h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>
	);
};
