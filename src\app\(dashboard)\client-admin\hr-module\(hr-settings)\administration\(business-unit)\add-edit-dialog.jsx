import { Button } from '@/components/ui/button';
import {
	Command,
	CommandEmpty,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { dummyEmployees } from '@/data/employeesData';
import {
	addBusinessUnit,
	updateBusinessUnit,
} from '@/lib/features/company-infrastructure/businessUnitSlice';
import { fetchAllEmployees } from '@/lib/features/employees/employeeSlice';
import {
	fetchCountryCities,
	fetchCities,
} from '@/lib/features/location/locationSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { addUpdateBusinessUnitSchema } from '@/lib/schemas/companySchema';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import debounce from 'lodash.debounce';
import { Check, ChevronsUpDown, Loader2, XCircle } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';

const BusinessUnitAddEditDialog = ({
	isAdd,
	title,
	desc,
	businessUnit,
	showAddEditDialog,
	setShowAddEditDialog,
}) => {
	const [searchTerm, setSearchTerm] = useState('');
	const dispatch = useAppDispatch();
	const { companyData } = useAppSelector((store) => store.companyDetails);
	const { employees } = useAppSelector((store) => store.employee);
	const { countryCities, cities, isLoadingCities } = useAppSelector(
		(store) => store.location
	);
	const employeeList = employees;

	const form = useForm({
		resolver: zodResolver(addUpdateBusinessUnitSchema),
		defaultValues: {
			businessUnits: isAdd
				? [{ name: '', location: '' }]
				: [{ _id: '', name: '', location: '', admin: '' }],
		},
		mode: 'onSubmit',
		reValidateMode: 'onSubmit',
	});

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: 'businessUnits',
	});

	useEffect(() => {
		if (!isAdd && businessUnit) {
			form.setValue('businessUnits', [
				{
					_id: businessUnit._id,
					name: businessUnit.name,
					location: businessUnit.location,
					admin: businessUnit.admin?.userId,
				},
			]);
		}
	}, [isAdd, businessUnit, form]);

	const debouncedFetchCities = useMemo(
		() =>
			debounce((search) => {
				dispatch(
					fetchCities({
						countryId: companyData?.companyDetails?.businessCountry._id,
						citySearchTerm: search,
					})
				);
			}, 500),
		[companyData?.companyDetails?.businessCountry, dispatch]
	);

	const handleSearchChange = (value) => {
		setSearchTerm(value);
		debouncedFetchCities(value);
	};

	useEffect(() => {
		return () => {
			debouncedFetchCities.cancel();
		};
	}, [debouncedFetchCities]);

	// useEffect(() => {
	// 	if (showAddEditDialog && countryCities.length === 0) {
	// 		dispatch(
	// 			fetchCountryCities(companyData?.companyDetails?.businessCountry?._id)
	// 		);
	// 	}
	// }, [showAddEditDialog, countryCities.length, dispatch, companyData]);

	useEffect(() => {
		if (showAddEditDialog && employees.length === 0) {
			dispatch(fetchAllEmployees());
		}
	}, [showAddEditDialog, employees.length, dispatch]);

	const onSubmit = async ({ businessUnits }) => {
		const payload = businessUnits.map(({ admin, ...rest }) =>
			admin ? { admin, ...rest } : rest
		);

		const result = isAdd
			? await dispatch(addBusinessUnit(payload))
			: await dispatch(updateBusinessUnit(payload[0]));

		if (
			addBusinessUnit.fulfilled.match(result) ||
			updateBusinessUnit.fulfilled.match(result)
		) {
			setShowAddEditDialog(false);
		}
	};

	return (
		<Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
			<DialogContent className="max-h-[80vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{desc}</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form
						id="branch-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="grid gap-4 max-h-[50vh] overflow-y-auto pt-2 pb-2 pr-2"
					>
						{fields.map((branch, index) => (
							<div
								key={branch.id}
								className="relative grid grid-cols-2 gap-4 border p-4 rounded-lg"
							>
								{isAdd && fields.length > 1 && (
									<button
										type="button"
										onClick={() => remove(index)}
										className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-200"
									>
										<XCircle className="text-red-500" size={16} />
									</button>
								)}
								<FormField
									control={form.control}
									name={`businessUnits.${index}.name`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Name</FormLabel>
											<FormControl>
												<Input {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name={`businessUnits.${index}.location`}
									render={({ field }) => (
										<FormItem>
											<FormLabel>Location</FormLabel>
											<Popover>
												<PopoverTrigger asChild>
													<FormControl>
														<Button
															variant="outline"
															role="combobox"
															className={cn(
																'w-full justify-between',
																!field.value && 'text-muted-foreground'
															)}
														>
															{field.value
																? cities.find(
																		({ city }) => city === field.value
																	)?.city || field.value
																: 'Select city'}
															{isLoadingCities && (
																<Loader2 className="h-4 w-4 animate-spin" />
															)}
															{!isLoadingCities && (
																<ChevronsUpDown className="opacity-50" />
															)}
														</Button>
													</FormControl>
												</PopoverTrigger>
												<PopoverContent className="w-full p-0">
													<Command>
														{/* Search Input with Debounce */}
														<CommandInput
															placeholder="Search City..."
															className="h-9"
															value={searchTerm}
															onValueChange={handleSearchChange}
														/>
														<CommandList>
															{cities.length === 0 ? (
																<CommandEmpty>No City found.</CommandEmpty>
															) : (
																cities.map(({ city, state }, idx) => (
																	<CommandItem
																		key={city + idx}
																		value={city}
																		onSelect={() =>
																			form.setValue(
																				`businessUnits.${index}.location`,
																				city
																			)
																		}
																	>
																		{`${city} (${state})`}
																		<Check
																			className={cn(
																				'ml-auto',
																				city === field.value
																					? 'opacity-100'
																					: 'opacity-0'
																			)}
																		/>
																	</CommandItem>
																))
															)}
														</CommandList>
													</Command>
												</PopoverContent>
											</Popover>
											<FormMessage />
										</FormItem>
									)}
								/>
								{!isAdd && (
									<FormField
										control={form.control}
										name={`businessUnits.${index}.admin`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Admin</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<SelectTrigger>
														<SelectValue placeholder="Select an admin" />
													</SelectTrigger>
													<SelectContent>
														<ScrollArea className="max-h-60 overflow-y-auto">
															<div>
																{employeeList.map((employee) => (
																	<SelectItem
																		key={employee._id}
																		value={employee._id}
																	>
																		{employee.personalDetails.name}
																	</SelectItem>
																))}
															</div>
														</ScrollArea>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}
							</div>
						))}
						{isAdd && (
							<Button
								type="button"
								variant="outline"
								onClick={() => append({ name: '', location: '', admin: '' })}
								className="mt-2"
							>
								Add Another Branch
							</Button>
						)}
					</form>
				</Form>
				<DialogFooter>
					<Button variant="outline" onClick={() => setShowAddEditDialog(false)}>
						Cancel
					</Button>
					<Button type="submit" form="branch-form">
						Confirm
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default BusinessUnitAddEditDialog;
