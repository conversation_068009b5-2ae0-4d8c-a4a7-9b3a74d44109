import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	tasks: [],
	employees: [],
	taskBoard: {},
	taskDetails: null,
	isLoading: false,
	error: null,
};

export const fetchEmployeesToAssign = createAsyncThunk(
	'tasks/fetchEmployeesToAssign',
	async (projectId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/tasks/employees/${projectId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const fetchTaskOfProject = createAsyncThunk(
	'tasks/fetchTaskOfProject',
	async (projectId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/tasks/project/${projectId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const fetchTaskBoardOfProject = createAsyncThunk(
	'tasks/fetchTaskBoardOfProject',
	async (projectId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/tasks/board/${projectId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const fetchTaskAssignedToEmployee = createAsyncThunk(
	'tasks/fetchTaskAssignedToEmployee',
	async (employeeId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/tasks/employee/${employeeId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const createTaskGroup = createAsyncThunk(
	'tasks/createTaskGroup',
	async (taskGroupData, { rejectWithValue, dispatch }) => {
		try {
			console.log(taskGroupData);
			const { data } = await customFetch.post('/tasks/group', taskGroupData);
			if (data.success) {
				dispatch(fetchTaskBoardOfProject(taskGroupData.projectId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const updateTaskGroup = createAsyncThunk(
	'tasks/updateTaskGroup',
	async (taskGroupData, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.patch('/tasks/group', taskGroupData);
			if (data.success) {
				dispatch(fetchTaskBoardOfProject(taskGroupData.projectId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const createTask = createAsyncThunk(
	'tasks/createTask',
	async (taskData, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.post('/tasks', taskData);
			if (data.success) {
				dispatch(fetchTaskBoardOfProject(taskData.projectId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const updateStatus = createAsyncThunk(
	'tasks/updateStatus',
	async ({ taskData }, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.patch(
				`/tasks/update-status`,
				taskData
			);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const fetchSingleTaskDetails = createAsyncThunk(
	'tasks/fetchSingleTaskDetails',
	async (taskId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/tasks/${taskId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const updateTask = createAsyncThunk(
	'tasks/updateTask',
	async (taskData, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.patch(`/tasks`, taskData);
			if (data.success) {
				dispatch(fetchSingleTaskDetails(taskData.taskId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const updateTaskCoverImage = createAsyncThunk(
	'tasks/updateTaskCoverImage',
	async ({ projectId, taskData }, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.patch(`/tasks`, taskData);
			if (data.success) {
				dispatch(fetchSingleTaskDetails(taskData.taskId));
				dispatch(fetchTaskBoardOfProject(projectId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const addTaskComment = createAsyncThunk(
	'tasks/addTaskComment',
	async (commentData, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.patch(`/tasks/comments`, commentData);
			if (data.success) {
				dispatch(fetchSingleTaskDetails(commentData.taskId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const updateTaskPosition = createAsyncThunk(
	'tasks/updateTaskPosition',
	async ({ projectId, positionData }, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.patch(`/tasks/position`, positionData);
			if (data.success) {
				dispatch(fetchTaskBoardOfProject(projectId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const updateTaskDescription = createAsyncThunk(
	'tasks/updateTaskDescription',
	async (descriptionData, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.patch(
				`/tasks/description`,
				descriptionData
			);
			if (data.success) {
				dispatch(fetchSingleTaskDetails(descriptionData.taskId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

const tasksSlice = createSlice({
	name: 'tasks',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchTaskOfProject.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchTaskOfProject.fulfilled, (state, action) => {
				state.isLoading = false;
				state.tasks = action.payload.data;
			})
			.addCase(fetchTaskOfProject.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload;
			})
			.addCase(fetchTaskBoardOfProject.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchTaskBoardOfProject.fulfilled, (state, action) => {
				state.isLoading = false;
				console.log(action.payload.data);
				state.taskBoard = action.payload.data;
			})
			.addCase(fetchTaskBoardOfProject.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload;
			})
			.addCase(fetchTaskAssignedToEmployee.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchTaskAssignedToEmployee.fulfilled, (state, action) => {
				state.isLoading = false;
				state.tasks = action.payload.data;
			})
			.addCase(fetchTaskAssignedToEmployee.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload;
			})
			.addCase(createTask.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(createTask.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(createTask.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateTask.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateTask.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				// toast.success(payload.message);
			})
			.addCase(updateTask.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateTaskCoverImage.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateTaskCoverImage.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				// toast.success(payload.message);
			})
			.addCase(updateTaskCoverImage.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(addTaskComment.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(addTaskComment.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(addTaskComment.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateTaskDescription.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateTaskDescription.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				// toast.success(payload.message);
			})
			.addCase(updateTaskDescription.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateTaskPosition.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateTaskPosition.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				// toast.success(payload.message);
			})
			.addCase(updateTaskPosition.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(createTaskGroup.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(createTaskGroup.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(createTaskGroup.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateTaskGroup.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateTaskGroup.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateTaskGroup.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateStatus.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateStatus.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateStatus.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchSingleTaskDetails.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchSingleTaskDetails.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.taskDetails = payload.data;
			})
			.addCase(fetchSingleTaskDetails.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchEmployeesToAssign.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchEmployeesToAssign.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.employees = payload.data;
			})
			.addCase(fetchEmployeesToAssign.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export default tasksSlice.reducer;
