import { z } from 'zod';

export const appointAdminSchema = z.object({
	employeeId: z.string().min(1, 'Employee ID is required'),
	companyId: z.string().min(1, 'Company ID is required'),
});

export const removeAdminSchema = z.object({
	currentAdminId: z.string().min(1, 'Current admin ID is required'),
	newAdminId: z.string().optional(),
	confirmation: z
		.string()
		.min(1, "Please type 'CONFIRM' to proceed")
		.refine((val) => val === 'CONFIRM', {
			message: "Please type 'CONFIRM' exactly to proceed",
		}),
});

export const changeCompanySchema = z.object({
	countryId: z.string().min(1, 'Country ID is required'),
});

export const createCompanySchema = z.object({
	businessName: z
		.string()
		.min(2, 'Business name must be at least 2 characters'),
	businessCountry: z.string().min(2, 'Please select a country'),
	registration: z.string().min(2, 'Registration number is required'),
	address: z.string().min(5, 'Address must be at least 5 characters'),
	currency: z.string().min(1, 'Please select a currency'),
	timeFormat: z.enum(['12h', '24h'], {
		required_error: 'Please select a time format',
	}),
	dateFormat: z.enum(['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY/MM/DD'], {
		required_error: 'Please select a date format',
	}),
});
