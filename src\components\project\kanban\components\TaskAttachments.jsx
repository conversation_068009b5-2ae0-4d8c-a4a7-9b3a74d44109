'use client';

import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
	Paperclip,
	Upload,
	FileText,
	FileImage,
	FileVideo,
	File,
	Download,
	Eye,
	X,
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * TaskAttachments Component
 * Renders the attachments section with image management
 */
export const TaskAttachments = ({
	attachments,
	coverImageUrl,
	onSetCoverImage,
	onRemoveCoverImage,
	onAttachmentUpload,
	onAttachmentPreview,
	onAttachmentDownload,
	onAttachmentDelete,
}) => {
	const fileInputRef = useRef(null);
	const [isUploading, setIsUploading] = useState(false);

	const handleFileSelect = () => {
		fileInputRef.current?.click();
	};

	const handleFileChange = async (event) => {
		const files = Array.from(event.target.files || []);
		if (files.length === 0) return;

		setIsUploading(true);
		try {
			if (onAttachmentUpload) {
				await onAttachmentUpload(files);
			}
		} catch (error) {
			console.error('Error uploading files:', error);
		} finally {
			setIsUploading(false);
			// Reset the input
			if (fileInputRef.current) {
				fileInputRef.current.value = '';
			}
		}
	};

	const getFileIcon = (attachment) => {
		const fileType = attachment.resource_type || attachment.type;
		const fileName = attachment.public_id || attachment.name || '';
		const extension = fileName.split('.').pop()?.toLowerCase();

		if (
			fileType === 'image' ||
			['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)
		) {
			return FileImage;
		}
		if (
			fileType === 'video' ||
			['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension)
		) {
			return FileVideo;
		}
		if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(extension)) {
			return FileText;
		}
		return File;
	};

	const formatFileSize = (bytes) => {
		if (!bytes) return '';
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
	};

	return (
		<div>
			<div className="flex items-center justify-between mb-3">
				<h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
					<Paperclip className="h-4 w-4" />
					Attachments
				</h3>
				<div className="flex items-center gap-2">
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={handleFileSelect}
						disabled={isUploading}
					>
						<Upload className="h-3 w-3 mr-1" />
						{isUploading ? 'Uploading...' : 'Add'}
					</Button>
					<Input
						ref={fileInputRef}
						type="file"
						multiple
						onChange={handleFileChange}
						className="hidden"
						accept="*/*"
					/>
				</div>
			</div>

			{attachments?.length > 0 ? (
				<div className="space-y-2">
					{attachments.map((attachment) => {
						const FileIcon = getFileIcon(attachment);
						const isImage =
							attachment.resource_type === 'image' ||
							['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(
								(attachment.public_id || attachment.name || '')
									.split('.')
									.pop()
									?.toLowerCase()
							);

						return (
							<div
								key={attachment._id || attachment.id}
								className={cn(
									'flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
									'border-gray-200 dark:border-gray-700',
									attachment.url === coverImageUrl &&
										'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700'
								)}
							>
								{/* File Preview/Icon */}
								<div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center flex-shrink-0">
									{isImage ? (
										<img
											src={attachment.url}
											alt={attachment.public_id || attachment.name}
											className="w-10 h-10 object-cover rounded"
										/>
									) : (
										<FileIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
									)}
								</div>

								{/* File Info */}
								<div className="flex-1 min-w-0">
									<p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
										{attachment.public_id || attachment.name || 'Unnamed file'}
									</p>
									<div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
										{attachment.bytes && (
											<span>{formatFileSize(attachment.bytes)}</span>
										)}
										{attachment.url === coverImageUrl && (
											<span className="text-blue-600 dark:text-blue-400 font-medium">
												Cover Image
											</span>
										)}
									</div>
								</div>

								{/* Action Buttons */}
								<div className="flex items-center gap-1">
									{/* Preview Button */}
									<Button
										type="button"
										variant="ghost"
										size="sm"
										onClick={() => onAttachmentPreview?.(attachment)}
										className="h-8 w-8 p-0"
										title="Preview"
									>
										<Eye className="h-4 w-4" />
									</Button>

									{/* Download Button */}
									<Button
										type="button"
										variant="ghost"
										size="sm"
										onClick={() => onAttachmentDownload?.(attachment)}
										className="h-8 w-8 p-0"
										title="Download"
									>
										<Download className="h-4 w-4" />
									</Button>

									{/* Cover Image Controls */}
									{isImage && (
										<>
											{attachment.url === coverImageUrl ? (
												<Button
													type="button"
													variant="ghost"
													size="sm"
													onClick={onRemoveCoverImage}
													className="h-8 px-2 text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
													title="Remove as cover"
												>
													Remove Cover
												</Button>
											) : (
												<Button
													type="button"
													variant="ghost"
													size="sm"
													onClick={() => onSetCoverImage(attachment.url)}
													className="h-8 px-2 text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
													title="Set as cover"
												>
													Make Cover
												</Button>
											)}
										</>
									)}

									{/* Delete Button */}
									<Button
										type="button"
										variant="ghost"
										size="sm"
										onClick={() => onAttachmentDelete?.(attachment)}
										className="h-8 w-8 p-0 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
										title="Delete"
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						);
					})}
				</div>
			) : (
				<p className="text-sm text-gray-500 dark:text-gray-400">
					No attachments yet
				</p>
			)}
		</div>
	);
};
