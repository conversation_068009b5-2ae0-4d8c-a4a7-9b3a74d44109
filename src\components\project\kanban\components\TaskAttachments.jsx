'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Paperclip } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * TaskAttachments Component
 * Renders the attachments section with image management
 */
export const TaskAttachments = ({
	attachments,
	coverImageUrl,
	onSetCoverImage,
	onRemoveCoverImage,
}) => {
	return (
		<div>
			<div className="flex items-center justify-between mb-3">
				<h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
					<Paperclip className="h-4 w-4" />
					Attachments
				</h3>
				<Button type="button" variant="outline" size="sm">
					Add
				</Button>
			</div>

			{attachments?.length > 0 ? (
				<div className="space-y-2">
					{attachments.map((attachment) => (
						<div
							key={attachment._id}
							className={cn(
								'flex items-center gap-3 p-2 border rounded-lg hover:bg-gray-50 transition-colors',
								attachment.url === coverImageUrl && 'bg-blue-50 border-blue-200'
							)}
						>
							<div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
								{attachment.resource_type === 'image' ? (
									<img
										src={attachment.url}
										alt={attachment.public_id}
										className="w-8 h-8 object-cover rounded"
									/>
								) : (
									<Paperclip className="h-4 w-4 text-gray-500" />
								)}
							</div>
							<div className="flex-1">
								<p className="text-sm font-medium">{attachment.public_id}</p>
								{/* <p className="text-xs text-gray-500">{attachment.size}</p> */}
								{attachment.url === coverImageUrl && (
									<p className="text-xs text-blue-600 font-medium">
										Cover Image
									</p>
								)}
							</div>
							{attachment.resource_type === 'image' && (
								<div className="flex gap-1">
									{attachment.url === coverImageUrl ? (
										<Button
											type="button"
											variant="ghost"
											size="sm"
											onClick={onRemoveCoverImage}
											className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
										>
											Remove Cover
										</Button>
									) : (
										<Button
											type="button"
											variant="ghost"
											size="sm"
											onClick={() => onSetCoverImage(attachment.url)}
											className="h-7 px-2 text-xs text-blue-600 hover:text-blue-700"
										>
											Make Cover
										</Button>
									)}
								</div>
							)}
						</div>
					))}
				</div>
			) : (
				<p className="text-sm text-gray-500">No attachments yet</p>
			)}
		</div>
	);
};
