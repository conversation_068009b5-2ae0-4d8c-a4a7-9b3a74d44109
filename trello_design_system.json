{"designSystem": {"name": "Trello-inspired Kanban Board Design System", "version": "1.0.0", "description": "A comprehensive design system for creating Trello-like kanban boards with cards, lists, and project management layouts"}, "colorPalette": {"primary": {"background": "#6b46c1", "backgroundGradient": "linear-gradient(135deg, #8b5cf6 0%, #6b46c1 100%)", "surface": "#ffffff", "surfaceSecondary": "#f8fafc"}, "accent": {"blue": "#3b82f6", "orange": "#f97316", "green": "#10b981", "purple": "#8b5cf6", "teal": "#14b8a6"}, "neutral": {"white": "#ffffff", "gray100": "#f1f5f9", "gray200": "#e2e8f0", "gray300": "#cbd5e1", "gray400": "#94a3b8", "gray500": "#64748b", "gray600": "#475569", "gray700": "#334155", "gray800": "#1e293b", "gray900": "#0f172a"}, "status": {"success": "#10b981", "warning": "#f59e0b", "error": "#ef4444", "info": "#3b82f6"}}, "typography": {"fontFamily": {"primary": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif", "mono": "'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace"}, "fontSizes": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem"}, "fontWeights": {"normal": 400, "medium": 500, "semibold": 600, "bold": 700}, "lineHeights": {"tight": 1.25, "normal": 1.5, "relaxed": 1.75}}, "spacing": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem", "3xl": "4rem"}, "borderRadius": {"none": "0", "sm": "0.125rem", "md": "0.375rem", "lg": "0.5rem", "xl": "0.75rem", "2xl": "1rem", "full": "9999px"}, "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)", "card": "0 2px 8px rgba(0, 0, 0, 0.1)", "cardHover": "0 4px 12px rgba(0, 0, 0, 0.15)"}, "layout": {"boardStructure": {"type": "horizontal-scrolling-columns", "minColumnWidth": "272px", "maxColumnWidth": "300px", "columnGap": "12px", "boardPadding": "12px", "headerHeight": "60px"}, "responsive": {"breakpoints": {"mobile": "640px", "tablet": "768px", "desktop": "1024px", "wide": "1280px"}, "mobileLayout": {"stackColumns": true, "showOneColumnAtTime": false, "enableSwipeNavigation": true}}}, "components": {"board": {"container": {"backgroundColor": "primary.background", "backgroundImage": "primary.backgroundGradient", "minHeight": "100vh", "padding": "spacing.md", "overflowX": "auto"}, "header": {"backgroundColor": "rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(10px)", "padding": "spacing.md spacing.lg", "borderRadius": "borderRadius.lg", "marginBottom": "spacing.lg"}}, "column": {"container": {"backgroundColor": "neutral.gray100", "borderRadius": "borderRadius.lg", "padding": "spacing.md", "minWidth": "layout.boardStructure.minColumnWidth", "maxWidth": "layout.boardStructure.maxColumnWidth", "height": "fit-content", "boxShadow": "shadows.md"}, "header": {"display": "flex", "justifyContent": "space-between", "alignItems": "center", "marginBottom": "spacing.md", "fontSize": "fontSizes.sm", "fontWeight": "fontWeights.semibold", "color": "neutral.gray700"}, "title": {"fontSize": "fontSizes.sm", "fontWeight": "fontWeights.semibold", "color": "neutral.gray700", "marginBottom": "spacing.sm"}, "addButton": {"width": "100%", "padding": "spacing.sm", "backgroundColor": "transparent", "border": "2px dashed", "borderColor": "neutral.gray300", "borderRadius": "borderRadius.md", "color": "neutral.gray500", "fontSize": "fontSizes.sm", "cursor": "pointer", "transition": "all 0.2s ease", "hoverState": {"borderColor": "neutral.gray400", "color": "neutral.gray600", "backgroundColor": "neutral.gray50"}}}, "card": {"container": {"backgroundColor": "neutral.white", "borderRadius": "borderRadius.lg", "padding": "spacing.md", "marginBottom": "spacing.sm", "boxShadow": "shadows.card", "border": "1px solid", "borderColor": "neutral.gray200", "cursor": "pointer", "transition": "all 0.2s ease"}, "hoverState": {"boxShadow": "shadows.cardHover", "transform": "translateY(-1px)", "borderColor": "neutral.gray300"}, "title": {"fontSize": "fontSizes.sm", "fontWeight": "fontWeights.medium", "color": "neutral.gray800", "lineHeight": "lineHeights.tight", "marginBottom": "spacing.sm"}, "description": {"fontSize": "fontSizes.xs", "color": "neutral.gray600", "lineHeight": "lineHeights.normal", "marginBottom": "spacing.sm"}, "metadata": {"display": "flex", "alignItems": "center", "gap": "spacing.sm", "fontSize": "fontSizes.xs", "color": "neutral.gray500"}, "badges": {"display": "flex", "gap": "spacing.xs", "marginTop": "spacing.sm"}}, "badge": {"small": {"padding": "2px 6px", "borderRadius": "borderRadius.sm", "fontSize": "fontSizes.xs", "fontWeight": "fontWeights.medium", "display": "inline-flex", "alignItems": "center", "gap": "spacing.xs"}, "variants": {"default": {"backgroundColor": "neutral.gray100", "color": "neutral.gray700"}, "primary": {"backgroundColor": "accent.blue", "color": "neutral.white"}, "success": {"backgroundColor": "status.success", "color": "neutral.white"}, "warning": {"backgroundColor": "status.warning", "color": "neutral.white"}}}, "progressIndicator": {"container": {"display": "flex", "alignItems": "center", "gap": "spacing.xs", "fontSize": "fontSizes.xs", "color": "neutral.gray600"}, "bar": {"width": "40px", "height": "4px", "backgroundColor": "neutral.gray200", "borderRadius": "borderRadius.full", "overflow": "hidden"}, "fill": {"height": "100%", "backgroundColor": "status.success", "borderRadius": "borderRadius.full", "transition": "width 0.3s ease"}}, "avatar": {"small": {"width": "20px", "height": "20px", "borderRadius": "borderRadius.full", "backgroundColor": "neutral.gray300", "display": "flex", "alignItems": "center", "justifyContent": "center", "fontSize": "fontSizes.xs", "fontWeight": "fontWeights.medium", "color": "neutral.white"}}, "button": {"primary": {"backgroundColor": "accent.blue", "color": "neutral.white", "padding": "spacing.sm spacing.md", "borderRadius": "borderRadius.md", "fontSize": "fontSizes.sm", "fontWeight": "fontWeights.medium", "border": "none", "cursor": "pointer", "transition": "all 0.2s ease", "hoverState": {"backgroundColor": "accent.blue", "transform": "translateY(-1px)", "boxShadow": "shadows.md"}}, "secondary": {"backgroundColor": "transparent", "color": "neutral.gray600", "padding": "spacing.sm spacing.md", "borderRadius": "borderRadius.md", "fontSize": "fontSizes.sm", "border": "1px solid", "borderColor": "neutral.gray300", "cursor": "pointer", "transition": "all 0.2s ease", "hoverState": {"backgroundColor": "neutral.gray50", "borderColor": "neutral.gray400"}}}}, "interactions": {"dragAndDrop": {"enableCardDragging": true, "enableColumnReordering": true, "dragPlaceholder": {"opacity": 0.5, "backgroundColor": "neutral.gray100", "border": "2px dashed", "borderColor": "neutral.gray300"}, "dropZone": {"backgroundColor": "rgba(59, 130, 246, 0.1)", "border": "2px dashed", "borderColor": "accent.blue"}}, "animations": {"cardHover": {"duration": "0.2s", "easing": "ease"}, "cardAdd": {"duration": "0.3s", "easing": "ease-out"}, "columnScroll": {"duration": "0.4s", "easing": "ease-in-out"}}}, "accessibility": {"focusIndicator": {"outline": "2px solid", "outlineColor": "accent.blue", "outlineOffset": "2px"}, "keyboardNavigation": {"enableArrowKeyNavigation": true, "enableTabNavigation": true, "enableEnterToSelect": true}, "screenReader": {"includeAriaLabels": true, "includeLiveRegions": true, "includeRoleAttributes": true}}, "stateManagement": {"cardStates": ["default", "hover", "selected", "dragging", "editing"], "columnStates": ["default", "drop<PERSON>ar<PERSON>", "collapsed", "editing"], "boardStates": ["loading", "ready", "editing", "readonly"]}, "contentGuidelines": {"cardTitle": {"maxLength": 100, "required": true, "placeholder": "Enter card title..."}, "cardDescription": {"maxLength": 500, "required": false, "placeholder": "Add a description..."}, "columnTitle": {"maxLength": 50, "required": true, "placeholder": "Enter list name..."}, "boardTitle": {"maxLength": 100, "required": true, "placeholder": "Enter board name..."}}}