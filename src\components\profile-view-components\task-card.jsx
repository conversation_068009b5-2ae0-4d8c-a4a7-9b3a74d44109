import React from 'react';
import * as Icons from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScrollArea } from '../ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

export function TaskCard({
	tasks = [
		{
			id: 1,
			name: 'Fix login issue in user auth flow',
			description:
				'Resolve the bug where users are unable to log in after recent updates to the authentication system.',
			status: 'pending',
			time: '5 hours ago',
			iconName: 'PlaneIcon',
			iconBackgroundColor: '#F9C5CD',
		},
		{
			id: 2,
			name: 'Update API documentation',
			description:
				'Revise the outdated API documentation to include the latest changes in endpoints and methods.',
			status: 'completed',
			time: '2 days ago',
			iconName: 'FileTextIcon',
			iconBackgroundColor: '#AFC7F9',
		},
		{
			id: 3,
			name: 'Optimize database queries',
			description:
				'Improve the performance of database queries for fetching user data to reduce load time during peak usage.',
			status: 'in progress',
			time: '1 day ago',
			iconName: 'HourglassIcon',
			iconBackgroundColor: '#91F6A8',
		},
		{
			id: 4,
			name: 'Implement new user onboarding flow',
			description:
				'Design and implement a new onboarding process for users to enhance their initial experience with the application.',
			status: 'locked',
			time: '3 days ago',
			iconName: 'LockIcon',
			iconBackgroundColor: '#90ADE7',
		},
	],
}) {
	return (
		<Card className="w-full h-[350px] flex flex-col">
			<CardHeader className="flex-shrink-0 flex flex-col gap-2">
				<CardTitle>Tasks</CardTitle>
			</CardHeader>
			<CardContent className="flex-1 overflow-hidden">
				<ScrollArea className="h-full">
					{tasks.length === 0 ? (
						<div className="text-center text-muted-foreground py-4">
							No tasks available.
						</div>
					) : (
						tasks.map((task) => {
							const Icon = Icons[task.iconName];

							return (
								<div
									key={task.id}
									className="flex items-center gap-2 px-2 py-1.5 hover:bg-card-hover rounded-md"
								>
									<div
										className="inline-flex items-center justify-center rounded-full text-sm font-medium shadow-sm p-2 h-8 w-8 flex-shrink-0"
										style={{ backgroundColor: task.iconBackgroundColor }}
									>
										{Icon ? (
											<Icon className="h-5 w-5 text-black dark:text-white" />
										) : (
											<span className="text-xs">?</span>
										)}
									</div>
									<div className="flex flex-col">
										<div className="font-semibold text-card-foreground dark:text-card-foreground-dark">
											{task.name}
										</div>
										<div className="text-sm text-muted-foreground dark:text-muted-foreground-dark">
											{task.description}
										</div>
										<div className="text-xs pt-1 text-muted-foreground dark:text-muted-foreground-dark opacity-80">
											{task.time}
										</div>
									</div>
								</div>
							);
						})
					)}
				</ScrollArea>
			</CardContent>
		</Card>
	);
}
