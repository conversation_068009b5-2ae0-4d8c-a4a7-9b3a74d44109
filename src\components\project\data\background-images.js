/**
 * Background images data for project cards and kanban boards
 * Using high-resolution landscape images (1920x1080, 16:9 ratio) for optimal quality
 */

export const backgroundImages = [
	{
		id: 1,
		name: 'By the Sea',
		url: 'https://picsum.photos/id/77/3840/2160',
		thumbnail: 'https://picsum.photos/id/77/400/225',
	},
	{
		id: 2,
		name: 'Blossoming Violets',
		url: 'https://picsum.photos/id/82/3840/2160',
		thumbnail: 'https://picsum.photos/id/82/400/225',
	},
	{
		id: 3,
		name: 'Snowy Mountain',
		url: 'https://picsum.photos/id/256/3840/2160',
		thumbnail: 'https://picsum.photos/id/256/400/225',
	},
	{
		id: 4,
		name: 'R<PERSON> Cart',
		url: 'https://picsum.photos/id/99/3840/2160',
		thumbnail: 'https://picsum.photos/id/99/400/225',
	},
	{
		id: 5,
		name: '<PERSON>ley',
		url: 'https://picsum.photos/id/98/3840/2160',
		thumbnail: 'https://picsum.photos/id/98/400/225',
	},
	{
		id: 6,
		name: 'Misty Morning Trees',
		url: 'https://picsum.photos/id/95/3840/2160',
		thumbnail: 'https://picsum.photos/id/95/400/225',
	},
	{
		id: 7,
		name: 'Whispers of Light',
		url: 'https://picsum.photos/id/94/3840/2160',
		thumbnail: 'https://picsum.photos/id/94/400/225',
	},
	{
		id: 8,
		name: 'Sunset Meadow',
		url: 'https://picsum.photos/id/93/3840/2160',
		thumbnail: 'https://picsum.photos/id/93/400/225',
	},
	{
		id: 9,
		name: 'Urban Bridge',
		url: 'https://picsum.photos/id/84/3840/2160',
		thumbnail: 'https://picsum.photos/id/84/400/225',
	},
	{
		id: 10,
		name: 'Rustic Cabin',
		url: 'https://picsum.photos/id/76/3840/2160',
		thumbnail: 'https://picsum.photos/id/76/400/225',
	},
	{
		id: 11,
		name: 'City Skyline',
		url: 'https://picsum.photos/id/74/3840/2160',
		thumbnail: 'https://picsum.photos/id/74/400/225',
	},
	{
		id: 12,
		name: 'Whitby Pier',
		url: 'https://picsum.photos/id/68/3840/2160',
		thumbnail: 'https://picsum.photos/id/68/400/225',
	},
	{
		id: 13,
		name: 'Salzburg Mountains',
		url: 'https://picsum.photos/id/61/3840/2160',
		thumbnail: 'https://picsum.photos/id/61/400/225',
	},
	{
		id: 14,
		name: 'Canyon Landscape',
		url: 'https://picsum.photos/id/46/3840/2160',
		thumbnail: 'https://picsum.photos/id/46/400/225',
	},
	{
		id: 15,
		name: 'Misty Coast',
		url: 'https://picsum.photos/id/44/3840/2160',
		thumbnail: 'https://picsum.photos/id/44/400/225',
	},
	{
		id: 16,
		name: 'Forest Creek',
		url: 'https://picsum.photos/id/28/3840/2160',
		thumbnail: 'https://picsum.photos/id/28/400/225',
	},
	{
		id: 17,
		name: 'Rays of Hope',
		url: 'https://picsum.photos/id/25/3840/2160',
		thumbnail: 'https://picsum.photos/id/25/400/225',
	},
	{
		id: 18,
		name: 'Grassy Forest Edge',
		url: 'https://picsum.photos/id/17/3840/2160',
		thumbnail: 'https://picsum.photos/id/17/400/225',
	},
	{
		id: 19,
		name: 'Calm Coast',
		url: 'https://picsum.photos/id/13/3840/2160',
		thumbnail: 'https://picsum.photos/id/13/400/225',
	},
	{
		id: 20,
		name: 'Forest Waterfall',
		url: 'https://picsum.photos/id/15/3840/2160',
		thumbnail: 'https://picsum.photos/id/15/400/225',
	},
	{
		id: 21,
		name: 'Hillside',
		url: 'https://picsum.photos/id/198/3840/2160',
		thumbnail: 'https://picsum.photos/id/198/400/225',
	},
];

export const coverImages = [
	{
		id: 1,
		name: 'By the Sea',
		url: 'https://picsum.photos/id/77/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/77/400/225?grayscale',
	},
	{
		id: 2,
		name: 'Blossoming Violets',
		url: 'https://picsum.photos/id/82/3840/2160',
		thumbnail: 'https://picsum.photos/id/82/400/225',
	},
	{
		id: 3,
		name: 'Snowy Mountain',
		url: 'https://picsum.photos/id/256/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/256/400/225?grayscale',
	},
	{
		id: 4,
		name: 'Rustic Cart',
		url: 'https://picsum.photos/id/99/3840/2160',
		thumbnail: 'https://picsum.photos/id/99/400/225',
	},
	{
		id: 5,
		name: 'Meadow Barley',
		url: 'https://picsum.photos/id/98/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/98/400/225?grayscale',
	},
	{
		id: 6,
		name: 'Misty Morning Trees',
		url: 'https://picsum.photos/id/95/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/95/400/225?grayscale',
	},
	{
		id: 7,
		name: 'Whispers of Light',
		url: 'https://picsum.photos/id/94/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/94/400/225?grayscale',
	},
	{
		id: 8,
		name: 'Sunset Meadow',
		url: 'https://picsum.photos/id/93/3840/2160',
		thumbnail: 'https://picsum.photos/id/93/400/225',
	},
	{
		id: 9,
		name: 'Urban Bridge',
		url: 'https://picsum.photos/id/84/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/84/400/225?grayscale',
	},
	{
		id: 10,
		name: 'Rustic Cabin',
		url: 'https://picsum.photos/id/76/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/76/400/225?grayscale',
	},
	{
		id: 11,
		name: 'City Skyline',
		url: 'https://picsum.photos/id/74/3840/2160',
		thumbnail: 'https://picsum.photos/id/74/400/225',
	},
	{
		id: 12,
		name: 'Whitby Pier',
		url: 'https://picsum.photos/id/68/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/68/400/225?grayscale',
	},
	{
		id: 13,
		name: 'Salzburg Mountains',
		url: 'https://picsum.photos/id/61/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/61/400/225?grayscale',
	},
	{
		id: 14,
		name: 'Canyon Landscape',
		url: 'https://picsum.photos/id/46/3840/2160',
		thumbnail: 'https://picsum.photos/id/46/400/225',
	},
	{
		id: 15,
		name: 'Misty Coast',
		url: 'https://picsum.photos/id/44/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/44/400/225?grayscale',
	},
	{
		id: 16,
		name: 'Forest Creek',
		url: 'https://picsum.photos/id/28/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/28/400/225?grayscale',
	},
	{
		id: 17,
		name: 'Rays of Hope',
		url: 'https://picsum.photos/id/25/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/25/400/225?grayscale',
	},
	{
		id: 18,
		name: 'Grassy Forest Edge',
		url: 'https://picsum.photos/id/17/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/17/400/225?grayscale',
	},
	{
		id: 19,
		name: 'Calm Coast',
		url: 'https://picsum.photos/id/13/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/13/400/225?grayscale',
	},
	{
		id: 20,
		name: 'Forest Waterfall',
		url: 'https://picsum.photos/id/15/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/15/400/225?grayscale',
	},
	{
		id: 21,
		name: 'Hillside',
		url: 'https://picsum.photos/id/198/3840/2160?grayscale',
		thumbnail: 'https://picsum.photos/id/198/400/225?grayscale',
	},
];

/**
 * Get background image by ID
 * @param {number} id - Image ID
 * @returns {object|null} Background image object or null if not found
 */
export const getBackgroundImageById = (id) => {
	return backgroundImages.find((image) => image.id === id) || null;
};
