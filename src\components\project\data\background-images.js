/**
 * Background images data for project cards and kanban boards
 * Using high-resolution landscape images (1920x1080, 16:9 ratio) for optimal quality
 */

export const backgroundImages = [
	{
		id: 1,
		name: 'Mountain Lake',
		url: 'https://picsum.photos/id/28/1920/1080',
		thumbnail: 'https://picsum.photos/id/28/400/225',
	},
	{
		id: 2,
		name: 'Forest Path',
		url: 'https://picsum.photos/id/12/1920/1080',
		thumbnail: 'https://picsum.photos/id/12/400/225',
	},
	{
		id: 3,
		name: 'Ocean Sunset',
		url: 'https://picsum.photos/id/41/1920/1080',
		thumbnail: 'https://picsum.photos/id/41/400/225',
	},
	{
		id: 4,
		name: 'City Skyline',
		url: 'https://picsum.photos/id/46/1920/1080',
		thumbnail: 'https://picsum.photos/id/46/400/225',
	},
	{
		id: 5,
		name: 'Desert Landscape',
		url: 'https://picsum.photos/id/56/1920/1080',
		thumbnail: 'https://picsum.photos/id/56/400/225',
	},
	{
		id: 6,
		name: 'Modern Architecture',
		url: 'https://picsum.photos/id/74/1920/1080',
		thumbnail: 'https://picsum.photos/id/74/400/225',
	},
	{
		id: 7,
		name: 'Rolling Hills',
		url: 'https://picsum.photos/id/116/1920/1080',
		thumbnail: 'https://picsum.photos/id/116/400/225',
	},
	{
		id: 8,
		name: 'Coastal Cliffs',
		url: 'https://picsum.photos/id/124/1920/1080',
		thumbnail: 'https://picsum.photos/id/124/400/225',
	},
	{
		id: 9,
		name: 'Urban Bridge',
		url: 'https://picsum.photos/id/141/1920/1080',
		thumbnail: 'https://picsum.photos/id/141/400/225',
	},
	{
		id: 10,
		name: 'Autumn Forest',
		url: 'https://picsum.photos/id/168/1920/1080',
		thumbnail: 'https://picsum.photos/id/168/400/225',
	},
	{
		id: 11,
		name: 'Snow Mountains',
		url: 'https://picsum.photos/id/176/1920/1080',
		thumbnail: 'https://picsum.photos/id/176/400/225',
	},
	{
		id: 12,
		name: 'River Valley',
		url: 'https://picsum.photos/id/178/1920/1080',
		thumbnail: 'https://picsum.photos/id/178/400/225',
	},
	{
		id: 13,
		name: 'Night City',
		url: 'https://picsum.photos/id/210/1920/1080',
		thumbnail: 'https://picsum.photos/id/210/400/225',
	},
	{
		id: 14,
		name: 'Tropical Beach',
		url: 'https://picsum.photos/id/213/1920/1080',
		thumbnail: 'https://picsum.photos/id/213/400/225',
	},
	{
		id: 15,
		name: 'Industrial Landscape',
		url: 'https://picsum.photos/id/218/1920/1080',
		thumbnail: 'https://picsum.photos/id/218/400/225',
	},
	{
		id: 16,
		name: 'Industrial Landscape',
		url: 'https://picsum.photos/id/234/1920/1080',
		thumbnail: 'https://picsum.photos/id/234/400/225',
	},
	{
		id: 17,
		name: 'Industrial Landscape',
		url: 'https://picsum.photos/id/235/1920/1080',
		thumbnail: 'https://picsum.photos/id/235/400/225',
	},
];

/**
 * Get background image by ID
 * @param {number} id - Image ID
 * @returns {object|null} Background image object or null if not found
 */
export const getBackgroundImageById = (id) => {
	return backgroundImages.find((image) => image.id === id) || null;
};
