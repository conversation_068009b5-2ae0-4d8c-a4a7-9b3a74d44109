import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '../ui/card';
import { ScrollArea } from '../ui/scroll-area';

export function LeaveCard() {
	return (
		<Card className="w-full h-[350px] flex flex-col">
			{' '}
			{/* Static height */}
			<CardHeader className="flex-shrink-0">
				<div className="flex items-center justify-between">
					<h2 className="text-base font-semibold text-card-foreground flex items-center gap-2 h-8">
						Apply for Leave
					</h2>
					<div className="h-8 flex items-center gap-2">
						<Button
							variant="outline"
							className="h-8 bg-green-100 text-green-800 hover:bg-green-200"
						>
							Apply
						</Button>
					</div>
				</div>
			</CardHeader>
			<CardContent className="flex-1 overflow-hidden">
				<ScrollArea className="h-full">
					{/* Placeholder structure matching task list */}
					<div className="flex flex-col gap-2 px-2 ">
						<div className="text-center text-muted-foreground py-4">
							No leaves applied yet
						</div>
					</div>
				</ScrollArea>
			</CardContent>
		</Card>
	);
}
