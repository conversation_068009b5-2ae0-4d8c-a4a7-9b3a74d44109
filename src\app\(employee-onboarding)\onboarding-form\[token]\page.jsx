'use client';

import React, { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { verifyOnboardingLink } from '@/lib/features/employees/employeeSlice';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export default function Page({ params }) {
	const dispatch = useAppDispatch();
	const router = useRouter();
	const { isLoading, onBoardingEmployee } = useAppSelector(
		(state) => state.employee
	);
	const [status, setStatus] = useState('verifying'); // 'verifying', 'success', 'error'

	useEffect(() => {
		const verify = async () => {
			try {
				const result = await dispatch(
					verifyOnboardingLink({ token: params.token })
				);

				if (verifyOnboardingLink.fulfilled.match(result)) {
					setStatus('success');
					// Optional: redirect to actual form after a delay
					// setTimeout(() => router.push('/onboarding-form/start'), 1500);
				} else {
					setStatus('error');
				}
			} catch (e) {
				setStatus('error');
			}
		};

		verify();
	}, [dispatch, params.token]);

	if (status === 'verifying' || isLoading) {
		return (
			<div className="flex items-center justify-center h-screen">
				<div className="text-center">
					<div className="flex items-center gap-2 justify-center text-muted-foreground text-lg">
						<Loader2 className="animate-spin" />
						<span>Verifying your identity...</span>
					</div>
				</div>
			</div>
		);
	}

	if (status === 'error') {
		return (
			<div className="flex items-center justify-center h-screen">
				<div className="text-center">
					<h2 className="text-xl font-semibold text-red-500">
						Invalid or expired link
					</h2>
					<p className="mt-2 text-muted-foreground">
						Please contact your admin for a new onboarding link.
					</p>
				</div>
			</div>
		);
	}

	// Success: Proceed to actual onboarding form
	return (
		<div className="container mx-auto py-10 px-4">
			<h1 className="text-2xl font-bold">
				Welcome {onBoardingEmployee?.name || ''}!
			</h1>
			<p className="mt-2 text-muted-foreground">
				Your identity has been verified. Please complete your onboarding form
				below.
			</p>

			{/* You can render the full form component here */}
		</div>
	);
}
