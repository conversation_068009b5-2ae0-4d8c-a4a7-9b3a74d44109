import { AppSidebar } from '@/components/app-sidebar';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import {
	SidebarInset,
	SidebarProvider,
	SidebarTrigger,
} from '@/components/ui/sidebar';
import { ResponsiveBreadcrumb } from './responsive-breadcrumbs';
import { usePathname } from 'next/navigation';
import { SuperAdminAppSidebar } from './super-admin-app-sidebar';
import { userRoles } from '@/lib/utils';
import { ModeToggle } from './theme-provider';
import { UserAppSidebar } from './employee-app-sidebar';
import { useEffect, useState } from 'react';

export function MainSidebar({ children, role }) {
	const pathname = usePathname();
	const segments = pathname.replace(/^\//, '').split('/');
	const [isKanbanBoard, setIsKanbanBoard] = useState(false);

	useEffect(() => {
		if (pathname.includes('projects-kanban/')) {
			setIsKanbanBoard(true);
		} else {
			setIsKanbanBoard(false);
		}
	}, [pathname]);

	return (
		<SidebarProvider
			style={{
				'--sidebar-width': '300px',
			}}
			defaultOpen={false}
		>
			{/* {console.log('role in main sidebar', role)} */}
			{role.includes(userRoles.SUPER_ADMIN) ? (
				<SuperAdminAppSidebar />
			) : role.includes(
					userRoles.BUSINESS_ADMIN ||
						userRoles.DEPARTMENT_ADMIN ||
						userRoles.EMPLOYEE ||
						userRoles.MODULE_ADMIN
			  ) ? (
				<UserAppSidebar />
			) : role.includes(
					userRoles.CLIENT_ADMIN || userRoles.GLORIFIED_CLIENT_ADMIN
			  ) ? (
				<AppSidebar />
			) : null}
			<SidebarInset>
				<header className="sticky top-0 z-50 flex shrink-0 items-center gap-2 shadow bg-background justify-between py-3 px-4 max-h-15 rounded-t-xl">
					<div className="flex items-center gap-2 px-4 capitalize">
						<SidebarTrigger className="-ml-1" />
						<Separator orientation="vertical" className="mr-2 h-4" />
						<ResponsiveBreadcrumb segments={segments} />
					</div>
					<ModeToggle />
				</header>
				<main
					style={{
						maxHeight: 'calc(100dvh - 5rem)',
						maxWidth: 'calc(100dvw - 5rem)',
					}}
					className={`flex flex-1 flex-col gap-4 ${!isKanbanBoard ? 'p-4' : ''}`}
				>
					{children}
				</main>
			</SidebarInset>
		</SidebarProvider>
	);
}
