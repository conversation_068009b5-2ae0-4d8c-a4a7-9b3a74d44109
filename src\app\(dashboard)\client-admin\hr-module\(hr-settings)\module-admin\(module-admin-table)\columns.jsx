'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { ArrowUpDown, Mail, Calendar, Shield, UserCog } from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDate } from '@/lib/utils';

// Module mapping for display purposes
const moduleNames = {
	hr: 'HR Module',
	leave: 'Leave Module',
	attendance: 'Attendance Module',
	expense: 'Expense Module',
	payroll: 'Payroll Module',
	project: 'Project Module',
	communication: 'Communication Module',
	performance: 'Performance Module',
	system: 'System Settings Module',
};

// Badge colors for different modules
const getModuleBadgeVariant = (module) => {
	switch (module) {
		case 'hr':
			return 'default'; // Primary color
		case 'leave':
			return 'secondary'; // Gray
		case 'attendance':
			return 'outline'; // Outline
		case 'expense':
			return 'destructive'; // Red
		case 'payroll':
			return { className: 'bg-amber-500 hover:bg-amber-600' }; // Amber
		case 'project':
			return { className: 'bg-emerald-500 hover:bg-emerald-600' }; // Green
		case 'communication':
			return { className: 'bg-sky-500 hover:bg-sky-600' }; // Sky blue
		case 'performance':
			return { className: 'bg-purple-500 hover:bg-purple-600' }; // Purple
		case 'system':
			return { className: 'bg-rose-500 hover:bg-rose-600' }; // Rose
		default:
			return 'outline'; // Default outline
	}
};

export const createColumns = (dispatch) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table.getIsAllPageRowsSelected()}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => (
				<Checkbox
					checked={row.getIsSelected()}
					onCheckedChange={(value) => row.toggleSelected(!!value)}
					aria-label="Select row"
				/>
			),
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: 'personalDetails.employeeOrgId',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Employee ID
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="font-medium">
					{row.original.personalDetails?.employeeOrgId || 'N/A'}
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'personalDetails.name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const name = row.original.personalDetails?.name || 'N/A';
				return (
					<div className="flex items-center gap-3">
						<Avatar className="h-8 w-8">
							<AvatarImage
								src={`/abstract-geometric-shapes.png?height=32&width=32&query=${encodeURIComponent(name)}`}
								alt={name}
							/>
							<AvatarFallback>
								{name
									.split(' ')
									.map((n) => n[0])
									.join('')
									.toUpperCase()
									.substring(0, 2)}
							</AvatarFallback>
						</Avatar>
						<span className="font-medium">{name}</span>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'email',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Email
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Mail className="size-4 text-muted-foreground" />
					<span>{row.original.email}</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'moduleAdminAccess',
			header: 'Module Access',
			cell: ({ row }) => {
				const modules = row.original.moduleAdminAccess || [];
				return (
					<div className="flex flex-wrap gap-1">
						{modules.length > 0 ? (
							modules.map((module) => (
								<Badge key={module} {...getModuleBadgeVariant(module)}>
									{moduleNames[module] || module}
								</Badge>
							))
						) : (
							<span className="text-muted-foreground">No modules assigned</span>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: 'role',
			header: 'Role',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Shield className="size-4 text-muted-foreground" />
					<Badge variant="secondary">Module Admin</Badge>
				</div>
			),
		},
		{
			accessorKey: 'reportingTo',
			header: 'Reporting To',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<UserCog className="size-4 text-muted-foreground" />
					<span>{row.original.reportingTo || 'N/A'}</span>
				</div>
			),
		},
		{
			accessorKey: 'personalDetails.dateOfJoining.$date',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Joining Date
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const date = row.original.personalDetails?.dateOfJoining?.$date;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="size-4 text-muted-foreground" />
						<span>{date ? formatDate(date) : 'N/A'}</span>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			id: 'actions',
			cell: ({ row }) => <DataTableRowActions row={row} dispatch={dispatch} />,
		},
	];
};
