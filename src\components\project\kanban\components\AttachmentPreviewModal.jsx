'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
	Download, 
	X, 
	FileText, 
	FileImage, 
	FileVideo, 
	File,
	ExternalLink,
	ZoomIn,
	ZoomOut,
	RotateCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * AttachmentPreviewModal Component
 * Comprehensive preview modal for all file types with download functionality
 */
export const AttachmentPreviewModal = ({ 
	isOpen, 
	onClose, 
	attachment, 
	onDownload 
}) => {
	const [imageZoom, setImageZoom] = useState(100);
	const [imageRotation, setImageRotation] = useState(0);

	if (!attachment) return null;

	const fileName = attachment.public_id || attachment.name || 'Unnamed file';
	const fileExtension = fileName.split('.').pop()?.toLowerCase();
	const fileType = attachment.resource_type || attachment.type;
	const fileUrl = attachment.url || attachment.src;

	const isImage = fileType === 'image' || 
		['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'].includes(fileExtension);
	const isVideo = fileType === 'video' || 
		['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(fileExtension);
	const isPdf = fileExtension === 'pdf';
	const isDocument = ['doc', 'docx', 'txt', 'rtf', 'odt'].includes(fileExtension);
	const isSpreadsheet = ['xls', 'xlsx', 'csv', 'ods'].includes(fileExtension);
	const isPresentation = ['ppt', 'pptx', 'odp'].includes(fileExtension);

	const formatFileSize = (bytes) => {
		if (!bytes) return '';
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
	};

	const handleDownload = () => {
		if (onDownload) {
			onDownload(attachment);
		} else {
			// Fallback download method
			const link = document.createElement('a');
			link.href = fileUrl;
			link.download = fileName;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		}
	};

	const handleZoomIn = () => setImageZoom(prev => Math.min(prev + 25, 300));
	const handleZoomOut = () => setImageZoom(prev => Math.max(prev - 25, 25));
	const handleRotate = () => setImageRotation(prev => (prev + 90) % 360);

	const renderPreview = () => {
		if (isImage) {
			return (
				<div className="flex flex-col items-center justify-center h-full">
					<div className="flex items-center gap-2 mb-4">
						<Button
							variant="outline"
							size="sm"
							onClick={handleZoomOut}
							disabled={imageZoom <= 25}
						>
							<ZoomOut className="h-4 w-4" />
						</Button>
						<span className="text-sm text-gray-600 dark:text-gray-400 min-w-[60px] text-center">
							{imageZoom}%
						</span>
						<Button
							variant="outline"
							size="sm"
							onClick={handleZoomIn}
							disabled={imageZoom >= 300}
						>
							<ZoomIn className="h-4 w-4" />
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={handleRotate}
						>
							<RotateCw className="h-4 w-4" />
						</Button>
					</div>
					<div className="overflow-auto max-h-[60vh] max-w-full">
						<img
							src={fileUrl}
							alt={fileName}
							className="max-w-none transition-transform duration-200"
							style={{
								transform: `scale(${imageZoom / 100}) rotate(${imageRotation}deg)`,
								transformOrigin: 'center'
							}}
						/>
					</div>
				</div>
			);
		}

		if (isVideo) {
			return (
				<div className="flex items-center justify-center h-full">
					<video
						controls
						className="max-w-full max-h-[60vh]"
						preload="metadata"
					>
						<source src={fileUrl} />
						Your browser does not support the video tag.
					</video>
				</div>
			);
		}

		if (isPdf) {
			return (
				<div className="flex flex-col items-center justify-center h-full">
					<iframe
						src={`${fileUrl}#toolbar=1`}
						className="w-full h-[60vh] border border-gray-300 dark:border-gray-600 rounded"
						title={fileName}
					/>
					<p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
						PDF preview may not work in all browsers. Use the download button to view the full document.
					</p>
				</div>
			);
		}

		// For other file types, show file info and download option
		return (
			<div className="flex flex-col items-center justify-center h-full text-center space-y-4">
				<div className="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
					{isDocument && <FileText className="h-12 w-12 text-blue-500" />}
					{isSpreadsheet && <FileText className="h-12 w-12 text-green-500" />}
					{isPresentation && <FileText className="h-12 w-12 text-orange-500" />}
					{!isDocument && !isSpreadsheet && !isPresentation && (
						<File className="h-12 w-12 text-gray-500" />
					)}
				</div>
				<div>
					<h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
						{fileName}
					</h3>
					<p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
						This file type cannot be previewed in the browser.
						<br />
						Click download to view the file.
					</p>
					<Button onClick={handleDownload} className="gap-2">
						<Download className="h-4 w-4" />
						Download File
					</Button>
				</div>
			</div>
		);
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[90vh] p-0">
				<DialogHeader className="p-4 border-b border-gray-200 dark:border-gray-700">
					<div className="flex items-center justify-between">
						<div className="flex-1 min-w-0">
							<DialogTitle className="text-lg font-semibold truncate">
								{fileName}
							</DialogTitle>
							<div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
								{attachment.bytes && (
									<span>{formatFileSize(attachment.bytes)}</span>
								)}
								{fileExtension && (
									<span className="uppercase">{fileExtension}</span>
								)}
							</div>
						</div>
						<div className="flex items-center gap-2 ml-4">
							<Button
								variant="outline"
								size="sm"
								onClick={handleDownload}
								className="gap-2"
							>
								<Download className="h-4 w-4" />
								Download
							</Button>
							{fileUrl && (
								<Button
									variant="outline"
									size="sm"
									onClick={() => window.open(fileUrl, '_blank')}
									className="gap-2"
								>
									<ExternalLink className="h-4 w-4" />
									Open
								</Button>
							)}
							<Button
								variant="ghost"
								size="sm"
								onClick={onClose}
								className="h-8 w-8 p-0"
							>
								<X className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</DialogHeader>
				<div className="p-4 h-[calc(90vh-120px)]">
					<ScrollArea className="h-full">
						{renderPreview()}
					</ScrollArea>
				</div>
			</DialogContent>
		</Dialog>
	);
};
