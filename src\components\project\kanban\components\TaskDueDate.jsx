'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { CalendarIcon, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

/**
 * TaskDueDate Component
 * Renders the due date picker for tasks
 */
export const TaskDueDate = ({ taskForm, onUpdateDueDate }) => {
	const handleDateSelect = (date) => {
		const formattedDate = date ? format(date, 'yyyy-MM-dd') : '';
		taskForm.setValue('dueDate', formattedDate);
		if (onUpdateDueDate) {
			onUpdateDueDate({ dueDate: formattedDate });
		}
	};

	const handleClearDate = () => {
		taskForm.setValue('dueDate', '');
		if (onUpdateDueDate) {
			onUpdateDueDate({ dueDate: '' });
		}
	};

	const parseDate = (dateString) => {
		if (!dateString) return undefined;
		return new Date(dateString);
	};

	return (
		<div className="mb-6">
			<div className="flex items-center justify-between mb-2">
				<h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
					<CalendarIcon className="h-4 w-4" />
					<span>Due Date</span>
				</h3>
			</div>

			<FormField
				control={taskForm.control}
				name="dueDate"
				render={({ field }) => (
					<FormItem className="flex flex-col">
						<div className="flex items-center gap-2">
							<Popover>
								<PopoverTrigger asChild>
									<FormControl>
										<Button
											variant="outline"
											className={cn(
												'w-[240px] pl-3 text-left font-normal',
												!field.value && 'text-muted-foreground'
											)}
										>
											{field.value ? (
												format(parseDate(field.value), 'PPP')
											) : (
												<span>Pick a date</span>
											)}
											<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
										</Button>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent className="w-auto p-0" align="start">
									<Calendar
										mode="single"
										selected={parseDate(field.value)}
										onSelect={handleDateSelect}
										disabled={(date) =>
											date < new Date(new Date().setHours(0, 0, 0, 0))
										}
										initialFocus
									/>
								</PopoverContent>
							</Popover>
							{field.value && (
								<Button
									type="button"
									variant="ghost"
									size="sm"
									onClick={handleClearDate}
									className="h-8 w-8 p-0"
								>
									<X className="h-4 w-4" />
								</Button>
							)}
						</div>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
};
