'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Draggable } from '@hello-pangea/dnd';
import { CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
	Calendar,
	MessageCircle,
	Paperclip,
	CheckSquare,
	Clock,
	AlertCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { getPriorityById } from '../data/kanban-data';
import { fetchSingleTaskDetails } from '@/lib/features/tasks/tasksSlice';
import { useAppDispatch } from '@/lib/hooks';

/**
 * KanbanCard Component
 * Individual task card within a kanban group following Trello-inspired design
 */
export const KanbanCard = ({ card, index, onClick, className, ...props }) => {
	return (
		<Draggable draggableId={card._id} index={index}>
			{(provided, snapshot) => (
				<div
					ref={provided.innerRef}
					{...provided.draggableProps}
					{...provided.dragHandleProps}
					className={cn(
						// Base styles following design system
						'cursor-pointer',
						'bg-white dark:bg-gray-800 border-2 border-transparent rounded-lg shadow-sm',
						'hover:border-2 hover:border-muted-foreground',

						// Dragging state
						snapshot.isDragging && 'opacity-90 shadow-xl scale-105',

						// Custom className
						className
					)}
					onClick={() => onClick(card)}
					tabIndex={0}
					role="article"
					aria-label={`Task: ${card.name}${card.description ? `. ${card.description}` : ''}`}
					{...props}
				>
					{/* Cover Image */}
					{card.coverImage && (
						<div className="relative w-full h-32 overflow-hidden rounded-t-lg">
							<Image
								src={card.coverImage}
								alt={card.name}
								width={400}
								height={128}
								className="w-full h-full object-cover transition-all duration-300"
								sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
								priority={false}
							/>
							<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 transition-opacity duration-300" />
						</div>
					)}

					<CardContent className="p-3 space-y-2">
						{/* Title */}
						<h3 className="text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-2 leading-tight">
							{card.name}
						</h3>

						{/* Description */}
						{card.description && (
							<p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 leading-relaxed">
								{card.description}
							</p>
						)}

						{/* Task Meta Information */}
						<div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
							{/* Creation Time */}
							{card.createdAt && (
								<div className="flex items-center gap-1">
									<Clock className="h-3 w-3" />
									<span>{new Date(card.createdAt).toLocaleDateString()}</span>
								</div>
							)}

							{/* Task Stats */}
							<div className="flex items-center gap-2">
								{/* Priority Badge */}
								{card.priority && (
									<Badge
										variant="outline"
										className={cn(
											'text-xs px-1 py-0 h-4',
											card.priority === 'high' &&
												'border-red-300 text-red-600 dark:border-red-600 dark:text-red-400',
											card.priority === 'medium' &&
												'border-yellow-300 text-yellow-600 dark:border-yellow-600 dark:text-yellow-400',
											card.priority === 'low' &&
												'border-green-300 text-green-600 dark:border-green-600 dark:text-green-400'
										)}
									>
										{card.priority}
									</Badge>
								)}

								{/* Attachments Count */}
								{card.media && card.media.length > 0 && (
									<div
										className="flex items-center gap-1"
										title={`${card.media.length} attachments`}
									>
										<Paperclip className="h-3 w-3" />
										<span>{card.media.length}</span>
									</div>
								)}

								{/* Comments Count */}
								{card.comments && card.comments.length > 0 && (
									<div
										className="flex items-center gap-1"
										title={`${card.comments.length} comments`}
									>
										<MessageCircle className="h-3 w-3" />
										<span>{card.comments.length}</span>
									</div>
								)}

								{/* Due Date Warning */}
								{card.dueDate && new Date(card.dueDate) < new Date() && (
									<div
										className="flex items-center gap-1 text-red-500 dark:text-red-400"
										title="Overdue"
									>
										<AlertCircle className="h-3 w-3" />
									</div>
								)}
							</div>
						</div>
					</CardContent>
				</div>
			)}
		</Draggable>
	);
};
