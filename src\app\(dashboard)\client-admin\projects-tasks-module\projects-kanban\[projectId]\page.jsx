'use client';

import { useCallback, useEffect, useState } from 'react';
import {
	KanbanBoard,
	KanbanHeader,
	sampleProjects,
} from '@/components/project';
import { getKanbanDataByProjectId } from '@/components/project/data/kanban-data';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { fetchSingleProjectDetails } from '@/lib/features/projects/projectsSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchTaskBoardOfProject } from '@/lib/features/tasks/tasksSlice';
import { SimpleLoader } from '@/components/loading-component';

export default function ProjectKanbanPage({ params }) {
	const { projectId } = params;
	const router = useRouter();
	const dispatch = useAppDispatch();
	const { projectDetails: project } = useAppSelector((store) => store.projects);
	const { isLoading } = useAppSelector((store) => store.tasks);
	// const project = sampleProjects.find((p) => p.id === parseInt(projectId));
	// const kanbanData = getKanbanDataByProjectId(parseInt(projectId));
	const [isGlassMode, setIsGlassMode] = useState(false);

	const fetchData = useCallback(async () => {
		await dispatch(fetchSingleProjectDetails(projectId));
		await dispatch(fetchTaskBoardOfProject(projectId));
	}, [dispatch, projectId]);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	const getBackgroundClass = () => {
		// if (!project) return 'bg-gradient-to-br from-purple-500 to-blue-600';
		if (!project) return '';

		if (project.details.bgType === 'color' && project.details.bgColor) {
			return project.details.bgColor;
		}

		return '';
	};

	const getBackgroundStyle = () => {
		if (!project) return {};

		if (project.details.bgType === 'image' && project.details.bgImage) {
			return {
				backgroundImage: `url(${project.details.bgImage.url})`,
				backgroundSize: 'cover',
				backgroundPosition: 'center',
				backgroundRepeat: 'no-repeat',
				backgroundAttachment: 'fixed',
			};
		} else if (project.details.bgType === 'color' && project.details.bgColor) {
			return {};
		}

		return {};
	};

	const handleBackClick = () => {
		router.push('/client-admin/projects-tasks-module/projects-kanban');
	};

	// if (isLoading) {
	// 	return (
	// 		<div className="min-h-screen items-center justify-center">
	// 			<SimpleLoader />
	// 		</div>
	// 	);
	// }
	return (
		<div
			className={cn(
				'h-[calc(100dvh-5rem)] rounded-bl-lg rounded-br-lg',
				getBackgroundClass()
			)}
			style={getBackgroundStyle()}
		>
			<div className="bg-black/20 h-full flex flex-col">
				{/* Board Header */}
				<KanbanHeader
					onBackClick={handleBackClick}
					onSettingsClick={() => console.log('Settings clicked')}
					onFilterClick={() => console.log('Filter clicked')}
					onMembersClick={() => console.log('Members clicked')}
					onMenuClick={() => console.log('Menu clicked')}
					onShareClick={() => console.log('Share clicked')}
					onCalendarClick={() => console.log('Calendar clicked')}
					onAnalyticsClick={() => console.log('Analytics clicked')}
					onGlassModeToggle={() => setIsGlassMode(!isGlassMode)}
					isGlassMode={isGlassMode}
				/>
				<div className="flex-1 w-[calc(100dvw-5rem)] p-3 rounded-bl-lg rounded-br-lg overflow-auto scrollbar-thin scrollbar-thumb-muted hover:scrollbar-thumb-muted-foreground scrollbar-track-transparent">
					<div className="min-w-max">
						<KanbanBoard isGlassMode={isGlassMode} />
					</div>
				</div>
			</div>
		</div>
	);
}
