import { z } from 'zod';

// Schema for creating task groups
export const createTaskGroupSchema = z.object({
	name: z.string().nonempty('Task group name is required'),
	projectId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project is required'),
	companyId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Company is required'),
});

// Schema for updating task groups
export const updateTaskGroupSchema = z.object({
	name: z.string().nonempty('Task group name is required'),
	bgColor: z
		.string()
		.regex(
			/^([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
			'Color must be a valid hex color code'
		)
		.optional(),
});

// Schema for creating tasks
export const createTaskSchema = z.object({
	name: z
		.string()
		.nonempty('Task name is required')
		.max(50, 'Task name must be at most 50 characters long'),
	code: z
		.string()
		.nonempty('Project code is required')
		.max(6, 'Project code must be at most 6 characters long'),
	projectId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project is required'),
	groupId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Group is required'),
});

// Schema for updating tasks
export const updateTaskSchema = z.object({
	taskId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Task ID is required'),
	name: z.string().optional(),
	assignedTo: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.optional(),
	dueDate: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Due date must be in YYYY-MM-DD format')
		.optional(),
	priority: z.enum(['low', 'medium', 'high']).optional(),
	coverImage: z.string().optional(),
	color: z
		.string()
		.regex(
			/^([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
			'Color must be a valid hex color code'
		)
		.optional(),
});

// Schema for adding comments with XSS protection
export const addCommentSchema = z.object({
	taskId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Task ID is required'),
	comment: z
		.string()
		.nonempty('Comment is required')
		.refine(
			(val) => {
				const maliciousStrings = [
					'<script',
					'<iframe',
					'<object',
					'<embed',
					'<link',
					'<style',
					'<meta',
					'<input',
					'<button',
					'<select',
					'<textarea',
					'<form',
					'<label',
					'<fieldset',
					'<legend',
					'<datalist',
					'<optgroup',
					'<option',
					'<keygen',
					'<output',
					'<progress',
					'<meter',
					'<details',
					'<summary',
					'<menuitem',
					'<menu',
				];

				const isMalicious = maliciousStrings.some((str) => val.includes(str));
				return !isMalicious;
			},
			{
				message: 'No malicious strings allowed in comments',
			}
		),
});

// Schema for updating task descriptions with security validation
export const updateTaskDescriptionSchema = z.object({
	taskId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Task ID is required'),
	description: z
		.string()
		.nonempty('Description is required')
		.refine(
			(val) => {
				// Allow safe HTML tags for rich text but block dangerous ones
				const dangerousStrings = [
					'<script',
					'<iframe',
					'<object',
					'<embed',
					'<link',
					'<style',
					'<meta',
					'<input',
					'<button',
					'<select',
					'<textarea',
					'<form',
					'<label',
					'<fieldset',
					'<legend',
					'<datalist',
					'<optgroup',
					'<option',
					'<keygen',
					'<output',
					'<progress',
					'<meter',
					'<details',
					'<summary',
					'<menuitem',
					'<menu',
					'javascript:',
					'vbscript:',
					'onload=',
					'onerror=',
					'onclick=',
					'onmouseover=',
				];

				const isDangerous = dangerousStrings.some((str) =>
					val.toLowerCase().includes(str.toLowerCase())
				);

				return !isDangerous;
			},
			{
				message: 'Description contains potentially dangerous content',
			}
		),
});
