'use client';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import {
	ArrowUpDown,
	Calendar,
	CheckCircle,
	PencilLine,
	Building,
	UserCheck,
	Network,
} from 'lucide-react';
import { DataTableRowActions } from './row-actions';
import { DataTableCellContent } from './details-popover';
import { formatDate } from '@/lib/utils';
import { Badge, badgeVariants } from '@/components/ui/badge';
import Link from 'next/link';
import { IconHierarchy } from '@tabler/icons-react';

export const createColumns = (dispatch) => {
	return [
		{
			id: 'select',
			header: ({ table }) => (
				<Checkbox
					checked={table.getIsAllPageRowsSelected()}
					onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Select all"
				/>
			),
			cell: ({ row }) => (
				<Checkbox
					checked={row.getIsSelected()}
					onCheckedChange={(value) => row.toggleSelected(!!value)}
					aria-label="Select row"
				/>
			),
			enableSorting: false,
			enableHiding: false,
		},
		{
			accessorKey: 'personalDetails.employeeOrgId',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Employee ID
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="font-medium">
					{row.original.personalDetails?.employeeOrgId ||
						row.original.employeeOrgId ||
						row.original._id.slice(0, 6) ||
						'N/A'}
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'isRegistrationComplete',
			header: 'Status',
			cell: ({ row }) => (
				<div className="flex items-center w-full">
					{row.original.userFlags?.isRegistrationComplete ? (
						<Badge>
							<div className="flex items-center justify-center gap-2 w-full">
								<CheckCircle className="size-3" /> Onboarded
							</div>
						</Badge>
					) : (
						<Link
							href={`/client-admin/hr-module/employee-onboarding?employeeId=${row.original._id}`}
							className={badgeVariants({ variant: 'outline' })}
						>
							<div className="flex items-center justify-center gap-2 w-full">
								<PencilLine className="size-3" /> Draft
							</div>
						</Link>
					)}
					{/* TODO: add antoher status for Inactive-KIV KIV stands for keep in view */}
				</div>
			),
		},
		{
			accessorKey: 'personalDetails.name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const fullName =
					row.original.personalDetails?.fullName ||
					row.original.personalDetails?.name ||
					'N/A';
				return (
					<DataTableCellContent
						type="employee"
						value={fullName}
						details={{
							id: row.original._id,
							email: row.original.personalDetails?.email,
							phone: row.original.personalDetails?.mobile,
							profilePhoto: row.original.personalDetails?.profilePhoto,
							designation: row.original.employmentDetails?.designation,
							department: row.original.employmentDetails?.department,
						}}
					/>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'businessUnit.name',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Business Unit
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Network className="size-4 text-muted-foreground" />
					<span>
						{row.original.businessUnit
							? `${row.original.businessUnit?.name} - (${row.original.businessUnit?.location})`
							: 'N/A'}
					</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'department.name',
			header: 'Department',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<Building className="size-4 text-muted-foreground" />
					<span>{row.original.department?.name || 'N/A'}</span>
				</div>
			),
		},
		{
			accessorKey: 'designation.name',
			header: 'Designation',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<UserCheck className="size-4 text-muted-foreground" />
					<span>{row.original.designation?.name || 'N/A'}</span>
				</div>
			),
		},
		{
			accessorKey: 'reportingTo',
			header: 'Reporting To',
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<IconHierarchy className="size-4 text-muted-foreground" />
					<span>{row.original.reportingTo || 'N/A'}</span>
				</div>
			),
		},
		{
			accessorKey: 'personalDetails.dateOfJoining',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Joining Date
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const date = row.original.personalDetails?.dateOfJoining;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="size-4 text-muted-foreground" />
						<span>{date ? formatDate(date) : 'N/A'}</span>
					</div>
				);
			},
			enableSorting: true,
		},
		{
			id: 'actions',
			cell: ({ row }) => {
				const employee = row.original;

				return (
					employee?.userFlags?.isRegistrationComplete && (
						<DataTableRowActions row={row} dispatch={dispatch} />
					)
				);
			},
		},
	];
};
