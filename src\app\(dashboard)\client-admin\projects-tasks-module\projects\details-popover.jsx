'use client';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Mail, Building, MapPin, Globe } from 'lucide-react';
import Image from 'next/image';

export function DataTableCellContent({ type, value, details }) {
	if (type === 'lead') {
		return (
			<Popover>
				<PopoverTrigger asChild>
					<Button
						variant="link"
						className="p-0 h-auto font-medium text-left flex items-center gap-2"
					>
						<Avatar className="h-6 w-6">
							<AvatarImage
								src={details.profilePhoto || '/placeholder.svg'}
								alt={value}
							/>
							<AvatarFallback>
								{value
									.split(' ')
									.map((n) => n[0])
									.join('')
									.toUpperCase()
									.substring(0, 2)}
							</AvatarFallback>
						</Avatar>
						{value}
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-80 p-0">
					<LeadDetailsCard details={details} value={value} />
				</PopoverContent>
			</Popover>
		);
	}

	return <div>{value}</div>;
}

function LeadDetailsCard({ details, value }) {
	const handleViewProfile = () => {
		window.location.href = `/employees/${details._id}`;
	};

	const getInitials = (name) => {
		return name
			.split(' ')
			.map((n) => n[0])
			.join('')
			.toUpperCase();
	};

	const company = details.companyId || {};
	const country = company.businessCountry || {};

	return (
		<Card className="border-0">
			<CardHeader className="pb-3">
				<div className="flex items-center gap-4">
					<Avatar className="h-16 w-16">
						<AvatarImage
							src={details.profilePhoto || '/placeholder.svg'}
							alt={value}
						/>
						<AvatarFallback>{getInitials(value)}</AvatarFallback>
					</Avatar>
					<div>
						<CardTitle className="text-lg">{value}</CardTitle>
						<CardDescription className="flex items-center gap-2 mt-1">
							Project Lead
						</CardDescription>
					</div>
				</div>
			</CardHeader>
			<CardContent className="pb-3">
				<div className="space-y-3">
					{/* Contact Information */}
					<div className="flex items-center gap-2">
						<Mail className="h-4 w-4 text-muted-foreground" />
						<span className="text-sm">{details.email}</span>
					</div>

					{/* Company Information */}
					{company.businessName && (
						<div className="flex items-center gap-2">
							<Building className="h-4 w-4 text-muted-foreground" />
							<div className="flex flex-col">
								<span className="text-sm font-medium">
									{company.businessName}
								</span>
								{company.logo && (
									<div className="mt-1">
										<Image
											width={32}
											height={32}
											src={company.logo || '/placeholder.svg'}
											alt={`${company.businessName} logo`}
											className="h-6 object-contain"
										/>
									</div>
								)}
							</div>
						</div>
					)}

					{/* Location Information */}
					{company.address && (
						<div className="flex items-start gap-2">
							<MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
							<span className="text-sm">{company.address}</span>
						</div>
					)}

					{/* Country Information */}
					{country.name && (
						<div className="flex items-center gap-2">
							<Globe className="h-4 w-4 text-muted-foreground" />
							<div className="flex flex-col">
								<span className="text-sm">
									{country.name} ({country.iso2})
								</span>
								<span className="text-xs text-muted-foreground">
									{country.currencyName} ({country.currencySymbol}
									{country.currency})
								</span>
							</div>
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}
