'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Plus,
	Calendar as CalendarIcon,
	<PERSON>,
	<PERSON>,
	<PERSON><PERSON>,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// Form validation schema
const createProjectSchema = z.object({
	code: z
		.string()
		.nonempty('Project code is required')
		.max(6, 'Project code must be at most 6 characters long'),
	name: z
		.string()
		.nonempty('Project name is required')
		.max(50, 'Project name must be at most 50 characters long'),
	description: z.string().optional(),
	status: z.enum(['active', 'inactive']).default('active'),
	lead: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project lead is required'),
	priority: z.enum(['low', 'medium', 'high'], {
		required_error: 'Please select a priority level',
	}),
	dueDate: z.date({
		required_error: 'Please select a due date',
	}),
	color: z.string().min(1, 'Please select a project color'),
});

// Available project colors
const projectColors = [
	{ name: 'Blue', value: 'bg-blue-500', class: 'bg-blue-500' },
	{ name: 'Green', value: 'bg-green-500', class: 'bg-green-500' },
	{ name: 'Purple', value: 'bg-purple-500', class: 'bg-purple-500' },
	{ name: 'Orange', value: 'bg-orange-500', class: 'bg-orange-500' },
	{ name: 'Red', value: 'bg-red-500', class: 'bg-red-500' },
	{ name: 'Pink', value: 'bg-pink-500', class: 'bg-pink-500' },
	{ name: 'Indigo', value: 'bg-indigo-500', class: 'bg-indigo-500' },
	{ name: 'Teal', value: 'bg-teal-500', class: 'bg-teal-500' },
];

// Sample team members (in real app, this would come from API)
const sampleTeamMembers = [
	{ id: 1, name: 'John Doe', email: '<EMAIL>', avatar: null },
	{ id: 2, name: 'Jane Smith', email: '<EMAIL>', avatar: null },
	{ id: 3, name: 'Mike Johnson', email: '<EMAIL>', avatar: null },
	{ id: 4, name: 'Sarah Wilson', email: '<EMAIL>', avatar: null },
	{ id: 5, name: 'Tom Brown', email: '<EMAIL>', avatar: null },
];

export function CreateProjectDialog({ children, onProjectCreate }) {
	const [open, setOpen] = useState(false);
	const [selectedMembers, setSelectedMembers] = useState([]);
	const [memberSearchQuery, setMemberSearchQuery] = useState('');

	const form = useForm({
		resolver: zodResolver(createProjectSchema),
		defaultValues: {
			code: '',
			title: '',
			description: '',
			priority: 'medium',
			dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
			color: 'bg-blue-500',
			status: '',
			lead: '',
		},
	});

	const onSubmit = (data) => {
		const newProject = {
			id: Date.now(), // In real app, this would be generated by backend
			title: data.title,
			description: data.description,
			status: 'planning',
			priority: data.priority,
			dueDate: data.dueDate.toISOString().split('T')[0],
			progress: 0,
			members: selectedMembers,
			tasksCount: 0,
			completedTasks: 0,
			color: data.color,
			isStarred: false,
		};

		onProjectCreate?.(newProject);
		setOpen(false);
		form.reset();
		setSelectedMembers([]);
		setMemberSearchQuery('');
	};

	const addMember = (member) => {
		if (!selectedMembers.find((m) => m.id === member.id)) {
			setSelectedMembers([...selectedMembers, member]);
		}
		setMemberSearchQuery('');
	};

	const removeMember = (memberId) => {
		setSelectedMembers(selectedMembers.filter((m) => m.id !== memberId));
	};

	const filteredMembers = sampleTeamMembers
		.filter(
			(member) =>
				member.name.toLowerCase().includes(memberSearchQuery.toLowerCase()) ||
				member.email.toLowerCase().includes(memberSearchQuery.toLowerCase())
		)
		.filter((member) => !selectedMembers.find((m) => m.id === member.id));

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>{children}</DialogTrigger>
			<DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Create New Project</DialogTitle>
					<DialogDescription>
						Set up a new project to organize your tasks and collaborate with
						your team.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
						{/* Project Title */}
						<FormField
							control={form.control}
							name="code"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Project Code</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter project code"
											{...field}
											maxLength={6}
										/>
									</FormControl>
									<FormDescription>
										This ID is randomly generated but can be edited once
										according to your preference.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Project Name</FormLabel>
									<FormControl>
										<Input
											placeholder="Enter project name"
											{...field}
											maxLength={50}
										/>
									</FormControl>
									<FormDescription>Maximum 50 characters</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Project Description */}
						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Description</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Describe your project..."
											className="min-h-[100px]"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="status"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Status</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select project status" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="active">Active</SelectItem>
											<SelectItem value="inactive">Inactive</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Priority and Due Date Row */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* Priority */}
							<FormField
								control={form.control}
								name="priority"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Priority</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder="Select priority" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="low">
													<div className="flex items-center gap-2">
														<div className="w-2 h-2 rounded-full bg-green-500" />
														Low
													</div>
												</SelectItem>
												<SelectItem value="medium">
													<div className="flex items-center gap-2">
														<div className="w-2 h-2 rounded-full bg-yellow-500" />
														Medium
													</div>
												</SelectItem>
												<SelectItem value="high">
													<div className="flex items-center gap-2">
														<div className="w-2 h-2 rounded-full bg-red-500" />
														High
													</div>
												</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Due Date */}
							<FormField
								control={form.control}
								name="dueDate"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Due Date</FormLabel>
										<Popover>
											<PopoverTrigger asChild>
												<FormControl>
													<Button
														variant="outline"
														className={cn(
															'w-full pl-3 text-left font-normal',
															!field.value && 'text-muted-foreground'
														)}
													>
														{field.value ? (
															format(field.value, 'PPP')
														) : (
															<span>Pick a date</span>
														)}
														<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
													</Button>
												</FormControl>
											</PopoverTrigger>
											<PopoverContent className="w-auto p-0" align="start">
												<Calendar
													mode="single"
													selected={field.value}
													onSelect={field.onChange}
													disabled={(date) =>
														date < new Date() || date < new Date('1900-01-01')
													}
													initialFocus
												/>
											</PopoverContent>
										</Popover>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name="lead"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Project Lead</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select a project lead" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{employees.map((employee) => (
												<SelectItem
													key={employee.reportingUserId}
													value={employee.reportingUserId}
												>
													{employee.reportingUserName}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormDescription>
										Assign a project lead to this project
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Project Color */}
						<FormField
							control={form.control}
							name="color"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-2">
										<Palette className="h-4 w-4" />
										Project Color
									</FormLabel>
									<FormControl>
										<div className="flex flex-wrap gap-2">
											{projectColors.map((color) => (
												<button
													key={color.value}
													type="button"
													onClick={() => field.onChange(color.value)}
													className={cn(
														'w-8 h-8 rounded-full border-2 transition-all',
														color.class,
														field.value === color.value
															? 'border-foreground scale-110'
															: 'border-muted hover:scale-105'
													)}
													title={color.name}
												/>
											))}
										</div>
									</FormControl>
									<FormDescription>
										Choose a color to help identify your project
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Team Members */}
						<div className="space-y-3">
							<FormLabel className="flex items-center gap-2">
								<Users className="h-4 w-4" />
								Team Members
							</FormLabel>

							{/* Selected Members */}
							{selectedMembers.length > 0 && (
								<div className="flex flex-wrap gap-2">
									{selectedMembers.map((member) => (
										<Badge
											key={member.id}
											variant="secondary"
											className="flex items-center gap-2 pr-1"
										>
											<Avatar className="h-4 w-4">
												<AvatarImage src={member.avatar} />
												<AvatarFallback className="text-xs">
													{member.name
														.split(' ')
														.map((n) => n[0])
														.join('')}
												</AvatarFallback>
											</Avatar>
											{member.name}
											<button
												type="button"
												onClick={() => removeMember(member.id)}
												className="ml-1 hover:bg-muted rounded-full p-0.5"
											>
												<X className="h-3 w-3" />
											</button>
										</Badge>
									))}
								</div>
							)}

							{/* Member Search */}
							<div className="relative">
								<Input
									placeholder="Search team members..."
									value={memberSearchQuery}
									onChange={(e) => setMemberSearchQuery(e.target.value)}
								/>
								{memberSearchQuery && filteredMembers.length > 0 && (
									<div className="absolute top-full left-0 right-0 z-10 mt-1 bg-popover border rounded-md shadow-md max-h-40 overflow-y-auto">
										{filteredMembers.map((member) => (
											<button
												key={member.id}
												type="button"
												onClick={() => addMember(member)}
												className="w-full flex items-center gap-3 p-3 hover:bg-muted text-left"
											>
												<Avatar className="h-6 w-6">
													<AvatarImage src={member.avatar} />
													<AvatarFallback className="text-xs">
														{member.name
															.split(' ')
															.map((n) => n[0])
															.join('')}
													</AvatarFallback>
												</Avatar>
												<div>
													<div className="font-medium">{member.name}</div>
													<div className="text-sm text-muted-foreground">
														{member.email}
													</div>
												</div>
											</button>
										))}
									</div>
								)}
							</div>
							<FormDescription>
								Add team members to collaborate on this project
							</FormDescription>
						</div>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => setOpen(false)}
							>
								Cancel
							</Button>
							<Button type="submit">
								<Plus className="h-4 w-4 mr-2" />
								Create Project
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
