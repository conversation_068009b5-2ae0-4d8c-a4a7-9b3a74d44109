'use client';

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { MessageSquare } from 'lucide-react';
import { RichTextEditor } from '../RichTextEditor';

/**
 * TaskComments Component
 * Renders the comments section with comment list and add comment form
 */
export const TaskComments = ({ task, commentForm, onAddComment }) => {
	return (
		<div className="w-80 border-l border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50 p-6 pl-3 flex flex-col">
			<div className="flex items-center gap-2 mb-4">
				<MessageSquare className="h-4 w-4 text-gray-500 dark:text-gray-400" />
				<h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
					Comments and activity
				</h3>
			</div>

			{/* Comments List */}
			<div className="flex-1 overflow-y-auto mb-4 space-y-3">
				{task?.comments?.map((comment) => (
					<div key={comment._id} className="flex gap-2">
						<Avatar className="h-6 w-6 mt-1">
							<AvatarFallback className="text-xs">
								{/* {comment.user.profilePhoto || 'U'} */}
								{'U'}
							</AvatarFallback>
						</Avatar>
						<div className="flex-1">
							<div className="bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm">
								<div className="flex items-center gap-2 mb-1">
									<span className="text-sm font-medium dark:text-gray-200">
										{comment?.user?.name || 'User'}
									</span>
									<span className="text-xs text-gray-500 dark:text-gray-400">
										{new Date(comment.createdAt).toLocaleDateString()}
									</span>
								</div>
								<div
									className="text-sm text-gray-700 dark:text-gray-300"
									dangerouslySetInnerHTML={{
										__html: comment.comment,
									}}
								/>
							</div>
						</div>
					</div>
				))}
			</div>

			{/* Add Comment */}
			<div className="border-t border-gray-200 dark:border-gray-700 pt-4">
				<Form {...commentForm}>
					<FormField
						control={commentForm.control}
						name="comment"
						render={({ field }) => (
							<FormItem>
								<FormControl>
									<RichTextEditor
										content={field.value}
										onChange={field.onChange}
										placeholder="Write a comment..."
										compact
									/>
								</FormControl>
								<FormMessage />
								<div className="flex justify-end mt-2">
									<Button
										type="button"
										size="sm"
										disabled={!field.value.trim()}
										onClick={(e) => {
											e.preventDefault();
											e.stopPropagation();
											commentForm.handleSubmit(onAddComment)();
										}}
									>
										Comment
									</Button>
								</div>
							</FormItem>
						)}
					/>
				</Form>
			</div>
		</div>
	);
};
