'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import { RichTextEditor } from '../RichTextEditor';

/**
 * TaskDescription Component
 * Renders the task description section with rich text editor
 */
export const TaskDescription = ({ 
	descriptionForm, 
	onUpdateDescription 
}) => {
	return (
		<div className="mb-6">
			<div className="flex items-center justify-between mb-2">
				<h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
					<span>Description</span>
				</h3>
			</div>

			<Form {...descriptionForm}>
				<form onSubmit={descriptionForm.handleSubmit(onUpdateDescription)}>
					<FormField
						control={descriptionForm.control}
						name="description"
						render={({ field }) => (
							<FormItem>
								<FormControl>
									<RichTextEditor
										content={field.value}
										onChange={field.onChange}
										placeholder="Add a more detailed description..."
									/>
								</FormControl>
								<FormMessage />
								<div className="flex items-center gap-2 justify-end mt-2">
									<Button
										type="submit"
										size="sm"
										className="h-7 px-2 text-xs"
									>
										Save Description
									</Button>
								</div>
							</FormItem>
						)}
					/>
				</form>
			</Form>
		</div>
	);
};
