'use client';

import React, { useState, useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
	Bold,
	Italic,
	List,
	ListOrdered,
	Link as LinkIcon,
	Quote,
	Strikethrough,
	Code,
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * RichTextEditor Component
 * TipTap-based rich text editor for descriptions and comments
 */
export const RichTextEditor = ({
	content = '',
	onChange,
	placeholder = 'Start typing...',
	compact = false,
	className,
}) => {
	const editor = useEditor({
		extensions: [
			StarterKit,
			Link.configure({
				openOnClick: false,
			}),
			Placeholder.configure({
				placeholder,
			}),
		],
		content,
		onUpdate: ({ editor }) => {
			onChange?.(editor.getHTML());
		},
	});

	// Update editor content when content prop changes
	useEffect(() => {
		if (editor && content !== editor.getHTML()) {
			editor.commands.setContent(content);
		}
	}, [content, editor]);

	if (!editor) {
		return null;
	}

	const toggleBold = () => editor.chain().focus().toggleBold().run();
	const toggleItalic = () => editor.chain().focus().toggleItalic().run();
	const toggleStrike = () => editor.chain().focus().toggleStrike().run();
	const toggleCode = () => editor.chain().focus().toggleCode().run();
	const toggleBulletList = () =>
		editor.chain().focus().toggleBulletList().run();
	const toggleOrderedList = () =>
		editor.chain().focus().toggleOrderedList().run();
	const toggleBlockquote = () =>
		editor.chain().focus().toggleBlockquote().run();

	return (
		<div className={cn('border rounded-md overflow-hidden', className)}>
			{/* Toolbar */}
			{!compact && (
				<div className="flex items-center gap-1 p-2 border-b bg-gray-50">
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('bold') ? 'bg-gray-200' : ''
						)}
						onClick={toggleBold}
					>
						<Bold className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('italic') ? 'bg-gray-200' : ''
						)}
						onClick={toggleItalic}
					>
						<Italic className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('strike') ? 'bg-gray-200' : ''
						)}
						onClick={toggleStrike}
					>
						<Strikethrough className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('code') ? 'bg-gray-200' : ''
						)}
						onClick={toggleCode}
					>
						<Code className="h-3 w-3" />
					</Button>
					<div className="w-px h-4 bg-gray-300 mx-1" />
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('bulletList') ? 'bg-gray-200' : ''
						)}
						onClick={toggleBulletList}
					>
						<List className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('orderedList') ? 'bg-gray-200' : ''
						)}
						onClick={toggleOrderedList}
					>
						<ListOrdered className="h-3 w-3" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						className={cn(
							'h-6 w-6 p-0',
							editor.isActive('blockquote') ? 'bg-gray-200' : ''
						)}
						onClick={toggleBlockquote}
					>
						<Quote className="h-3 w-3" />
					</Button>
				</div>
			)}

			{/* Editor Content */}
			<EditorContent
				editor={editor}
				className={cn(
					'prose prose-sm max-w-none focus:outline-none',
					compact ? 'min-h-[60px]' : 'min-h-[120px]',
					'[&_.ProseMirror]:p-3 [&_.ProseMirror]:focus:outline-none',
					'[&_.ProseMirror]:text-sm [&_.ProseMirror]:text-gray-700',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:content-[attr(data-placeholder)]',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:text-gray-400',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:float-left',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:pointer-events-none',
					'[&_.ProseMirror_p.is-editor-empty:first-child::before]:h-0'
				)}
			/>
		</div>
	);
};
