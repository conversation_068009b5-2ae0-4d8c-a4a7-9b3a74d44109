import { customFetch, showErrors } from '@/lib/utils';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	isLoading: false,
	employees: [],
	employeeDetails: null,
	step: 1,
	userProfile: null,
	onBoardingEmployee: null,
};

export const fetchProfilePageDetails = createAsyncThunk(
	'employee/fetchProfilePageDetails',
	async (employeeId, thunkAPI) => {
		try {
			const { data } = await customFetch.get(
				`/employees/profile/${employeeId}`
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchAllEmployees = createAsyncThunk(
	'employee/fetchAllEmployees',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/employees');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchEmployeeDetails = createAsyncThunk(
	'employee/fetchEmployeeDetails',
	async (id, thunkAPI) => {
		try {
			const { data } = await customFetch.get(`/employees/${id}`);
			if (data.data.userFlags) {
				const {
					isPersonalDetailsComplete,
					isQualificationDetailsComplete,
					isContactDetailsComplete,
					isEmploymentDetailsComplete,
					isEarningsDetailsComplete,
					isBenefitsDetailsComplete,
					isClientRegistrationAsEmployeeComplete,
				} = data.data.userFlags;
				// if (isBenefitsDetailsComplete === false) {
				// 	thunkAPI.dispatch(setEmployeeRegistrationSteps(6));
				// }
				if (isEarningsDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(5));
				}
				if (isEmploymentDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(4));
				}
				if (isContactDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(3));
				}
				if (isQualificationDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(2));
				}
				if (isPersonalDetailsComplete === false) {
					thunkAPI.dispatch(setEmployeeRegistrationSteps(1));
				}
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const registerEmployee = createAsyncThunk(
	'employee/registerEmployee',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/register',
				employeeDetails,
				{
					headers: {
						'Content-Type': 'multipart/form-data',
					},
				}
			);

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEmployeeQualificationsDetails = createAsyncThunk(
	'employee/updateEmployeeQualificationsDetails',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/qualifications',
				employeeDetails,
				{
					headers: {
						'Content-Type': 'multipart/form-data',
					},
				}
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEmployeeContactDetails = createAsyncThunk(
	'employee/updateEmployeeContactDetails',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/contacts',
				employeeDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEmployeeEmploymentDetails = createAsyncThunk(
	'employee/updateEmployeeEmploymentDetails',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/employment',
				employeeDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEmployeeEarningsDetails = createAsyncThunk(
	'employee/updateEmployeeEarningsDetails',
	async (earningsDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/earnings',
				earningsDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const onboardEmployeeUsingLink = createAsyncThunk(
	'employee/onboardEmployeeUsingLink',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/onboarding-link',
				employeeDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const verifyOnboardingLink = createAsyncThunk(
	'employee/verifyOnboardingLink',
	async (employeeDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.get(
				`/employees/verify-link/${employeeDetails.token}`,
				employeeDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const employeeRegistrationSlice = createSlice({
	name: 'employee',
	initialState,
	reducers: {
		resetEmployeeDetails: (state, { payload }) => {
			state.employeeDetails = null;
		},
		setEmployeeRegistrationSteps: (state, { payload }) => {
			state.step = payload;
		},
		setClientAdminIdAsEmployeeId: (state, { payload }) => {
			state.employeeDetails = {
				...state.employeeDetails,
				employeeId: payload,
			};
			// console.log(state.employeeDetails)
		},
	},
	extraReducers: (builder) => {
		builder
			.addCase(registerEmployee.pending, (state) => {
				state.isLoading = true;
				toast.info('Submitting Personal Details...');
			})
			.addCase(registerEmployee.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.employeeDetails = payload.data.employee;
				state.step = 2;
				toast.success(payload.message);
			})
			.addCase(registerEmployee.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchAllEmployees.pending, (state, { payload }) => {
				state.isLoading = true;
			})
			.addCase(fetchAllEmployees.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.employees = payload.data.employees;
			})
			.addCase(fetchAllEmployees.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(
				updateEmployeeQualificationsDetails.pending,
				(state, { payload }) => {
					state.isLoading = true;
					toast.info('Updating Qualifications...');
				}
			)
			.addCase(
				updateEmployeeQualificationsDetails.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					state.step = 3;
					toast.success(payload.message);
				}
			)
			.addCase(
				updateEmployeeQualificationsDetails.rejected,
				(state, { payload }) => {
					state.isLoading = false;
					showErrors(payload);
				}
			)
			.addCase(updateEmployeeContactDetails.pending, (state, { payload }) => {
				state.isLoading = true;
				toast.info('Updating Contact Details...');
			})
			.addCase(updateEmployeeContactDetails.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.step = 4;
				toast.success(payload.message);
			})
			.addCase(updateEmployeeContactDetails.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchEmployeeDetails.pending, (state, { payload }) => {
				state.isLoading = true;
			})
			.addCase(fetchEmployeeDetails.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.employeeDetails = payload.data;
			})
			.addCase(fetchEmployeeDetails.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(
				updateEmployeeEmploymentDetails.pending,
				(state, { payload }) => {
					state.isLoading = true;
					toast.info('Updating Employment Details...');
				}
			)
			.addCase(
				updateEmployeeEmploymentDetails.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					state.step = 5;
					toast.success(payload.message);
				}
			)
			.addCase(
				updateEmployeeEmploymentDetails.rejected,
				(state, { payload }) => {
					state.isLoading = false;
					showErrors(payload);
				}
			)
			.addCase(updateEmployeeEarningsDetails.pending, (state, { payload }) => {
				state.isLoading = true;
				toast.info('Updating Earnings Details...');
			})
			.addCase(
				updateEmployeeEarningsDetails.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					state.step = 1;
					toast.success(payload.message);
				}
			)
			.addCase(updateEmployeeEarningsDetails.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchProfilePageDetails.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchProfilePageDetails.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.userProfile = payload.data;
			})
			.addCase(fetchProfilePageDetails.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(onboardEmployeeUsingLink.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(onboardEmployeeUsingLink.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(onboardEmployeeUsingLink.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(verifyOnboardingLink.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(verifyOnboardingLink.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.onBoardingEmployee = payload.data;
				// toast.success(payload.message);
			})
			.addCase(verifyOnboardingLink.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export const {
	setEmployeeRegistrationSteps,
	setClientAdminIdAsEmployeeId,
	resetEmployeeDetails,
} = employeeRegistrationSlice.actions;

export default employeeRegistrationSlice.reducer;
