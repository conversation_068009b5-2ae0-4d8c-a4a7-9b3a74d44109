import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import {
	User,
	Mail,
	Phone,
	Calendar,
	MapPin,
	Users,
	GraduationCap,
	Briefcase,
	DollarSign,
	Shield,
	AlertCircle,
	Edit,
	Wallet,
	ContactIcon,
	Building,
} from 'lucide-react';
import Image from 'next/image';
import { formatDate } from '@/lib/utils';
import EditEmployeePersonalDetailsForm from './edit-employee-personal-details-form';
import EditEmployeeFamilyDetailsForm from './edit-employee-family-details-form';
import EditEmployeeEducationDetailsForm from './edit-employee-education-details-form';
import EditEmployeeExperienceDetailsForm from './edit-employee-experience-details-form';
import EditEmployeeContactDetailsForm from './edit-employee-contact-details-form';
import EditEmployeeEmploymentDetailsForm from './edit-employee-employment-details-form';
import EditEmployeeEarningDetailsForm from './edit-employee-earning-details-form';
import EditEmployeeBenefitDetailsForm from './edit-employee-benefit-details-form';

// Reusable UpdateDialog component
const UpdateDialog = ({ title, description, children }) => (
	<Dialog>
		<DialogTrigger asChild>
			<Button variant="outline" size="sm" className="ml-auto">
				<Edit className="h-4 w-4 mr-2" />
				Edit
			</Button>
		</DialogTrigger>
		<DialogContent className="sm:max-w-[425px]">
			<DialogHeader>
				<DialogTitle>{title}</DialogTitle>
				<DialogDescription>{description}</DialogDescription>
			</DialogHeader>
			{children}
		</DialogContent>
	</Dialog>
);

export default function EmployeeView({ employee }) {
	// Use the provided employee data or the sample data
	const employeeData = employee || sampleEmployee;
	console.log(` EmployeeView - employeeData:`, employeeData);
	const {
		personalDetails,
		familyDetails,
		education,
		experience,
		contactDetails,
		skills,
		employmentDetails,
		benefitDetails,
		earnings,
		userFlags,
		_id: employeeId,
	} = employeeData;

	const getInitials = (name) => {
		console.log(` getInitials - name:`, name);
		return name
			.split(' ')
			.map((n) => n[0])
			.join('')
			.toUpperCase();
	};

	// Format date function

	// Calculate completion percentage
	const calculateCompletion = () => {
		const fields = [
			userFlags.isRegistrationComplete,
			userFlags.isQualificationDetailsComplete,
			userFlags.isFamilyDetailsComplete,
			userFlags.isSkillsDetailsComplete,
			userFlags.isContactDetailsComplete,
			userFlags.isEmploymentDetailsComplete,
			userFlags.isEarningsDetailsComplete,
			userFlags.isBenefitsDetailsComplete,
			userFlags.isPersonalDetailsComplete,
		];

		const completedFields = fields.filter((field) => field).length;
		return Math.round((completedFields / fields.length) * 100);
	};

	// Format working days
	const formatWorkingDays = (days) => {
		if (!days) return 'N/A';

		const workingDaysMap = {
			'5_DAYS': '5 Days',
			'5.5_DAYS': '5.5 Days',
			'6_DAYS': '6 Days',
		};

		return workingDaysMap[days] || days;
	};

	const formatHalfDay = (halfDay) => {
		if (!halfDay) return 'N/A';

		const halfDayMap = {
			FIRST_HALF: 'First Half',
			SECOND_HALF: 'Second Half',
		};

		return halfDayMap[halfDay] || halfDay;
	};

	return (
		<div className="flex flex-col min-h-screen">
			{/* Fixed Header */}
			<div className="bg-background border-b">
				<div className="container mx-auto py-4">
					<div className="flex flex-col md:flex-row items-start md:items-center gap-6">
						<Avatar className="w-24 h-24 z-0 border-4 border-primary/10">
							<AvatarImage
								src={personalDetails?.profilePhoto}
								alt={personalDetails?.name}
							/>
							<AvatarFallback className="text-xl font-semibold uppercase">
								{getInitials(personalDetails?.name)}
							</AvatarFallback>
						</Avatar>

						<div className="flex-1">
							<div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
								<div>
									<h1 className="text-2xl font-semibold">
										{personalDetails?.name}
									</h1>
									<div className="flex flex-wrap items-center gap-2 mt-1 text-muted-foreground">
										<Mail className="h-4 w-4" />
										<span>{employeeData?.email}</span>
										<span className="mx-1">•</span>
										<Phone className="h-4 w-4" />
										<span>{`${personalDetails?.countryDialCode}-${personalDetails?.mobile}`}</span>
									</div>
									{employmentDetails?.designation && (
										<div className="mt-1 text-sm">
											<Badge variant="outline" className="mr-2">
												{employmentDetails.designation?.name}
											</Badge>
											<span className="text-muted-foreground">
												{employmentDetails.department?.name} •{' '}
												{employmentDetails.businessUnit?.name} •{' '}
												{personalDetails?.employeeOrgId}
											</span>
										</div>
									)}
								</div>

								<div className="flex flex-col gap-2">
									<Badge
										variant={
											calculateCompletion() === 100 ? 'default' : 'outline'
										}
										className="px-3 py-1"
									>
										Profile {calculateCompletion()}% Complete
									</Badge>
									<div className="text-sm text-muted-foreground">
										<Calendar className="h-4 w-4 inline mr-1" />
										Joined {formatDate(personalDetails?.dateOfJoining)}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Main content */}
			<div className="flex-grow container mx-auto py-6 px-4 w-full">
				<Tabs defaultValue="personal" className="w-full">
					<TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 w-full mb-4">
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="personal"
						>
							<User className="h-4 w-4 mr-2" />
							<span className="hidden sm:inline">Personal</span>
						</TabsTrigger>
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="family"
						>
							<Users className="h-4 w-4 mr-2" />
							<span className="hidden sm:inline">Family</span>
						</TabsTrigger>
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="education"
						>
							<GraduationCap className="h-4 w-4 mr-2" />
							<span className="hidden sm:inline">Education</span>
						</TabsTrigger>
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="experience"
						>
							<Briefcase className="h-4 w-4 mr-2" />
							<span className="hidden sm:inline">Experience</span>
						</TabsTrigger>
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="contacts"
						>
							<ContactIcon className="h-4 w-4 mr-2" />
							<span className="hidden sm:inline">Contacts</span>
						</TabsTrigger>
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="employment"
						>
							<Building className="h-4 w-4 mr-2" />
							<span className="hidden sm:inline">Employment</span>
						</TabsTrigger>
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="earnings"
						>
							<Wallet className="h-4 w-4 mr-2" />
							<span className="hidden sm:inline">Earnings</span>
						</TabsTrigger>
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="benefits"
						>
							<Shield className="h-4 w-4 mr-2" />
							<span className="hidden sm:inline">Benefits</span>
						</TabsTrigger>
					</TabsList>

					{/* Personal Details Tab */}
					<TabsContent value="personal">
						<EditEmployeePersonalDetailsForm
							employeeId={employeeId}
							personalDetails={{
								...personalDetails,
								email: employeeData.email,
							}}
						/>
					</TabsContent>

					{/* Family Details Tab */}
					<TabsContent value="family">
						<EditEmployeeFamilyDetailsForm
							employeeId={employeeId}
							familyDetails={familyDetails}
						/>
					</TabsContent>

					{/* Education Tab */}
					<TabsContent value="education">
						<EditEmployeeEducationDetailsForm
							employeeId={employeeId}
							education={education}
							skills={skills}
						/>
					</TabsContent>

					{/* Experience Tab */}
					<TabsContent value="experience">
						<EditEmployeeExperienceDetailsForm
							employeeId={employeeId}
							experience={experience}
						/>
					</TabsContent>

					{/* Contacts Tab */}
					<TabsContent value="contacts">
						<EditEmployeeContactDetailsForm
							employeeId={employeeId}
							contactDetails={contactDetails}
						/>
					</TabsContent>

					{/* Employment Tab */}
					<TabsContent value="employment">
						<EditEmployeeEmploymentDetailsForm
							employeeId={employeeId}
							employmentDetails={employmentDetails}
							employee={employee}
						/>
					</TabsContent>

					{/* Earnings Tab */}
					<TabsContent value="earnings">
						<EditEmployeeEarningDetailsForm
							employeeId={employeeId}
							earnings={earnings}
						/>
					</TabsContent>

					{/* Benefits Tab */}
					<TabsContent value="benefits">
						<EditEmployeeBenefitDetailsForm
							employeeId={employeeId}
							benefits={benefitDetails}
						/>
					</TabsContent>
				</Tabs>
			</div>
		</div>
	);
}
