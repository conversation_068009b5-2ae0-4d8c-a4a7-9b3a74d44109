'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import {
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';

/**
 * TaskTitle Component
 * Renders the editable task title field
 */
export const TaskTitle = ({ taskForm }) => {
	return (
		<div className="mb-6">
			<FormField
				control={taskForm.control}
				name="name"
				render={({ field }) => (
					<FormItem>
						<FormControl>
							<Input
								{...field}
								placeholder="Task title..."
								className="text-3xl font-semibold border-none p-0 h-auto focus-visible:ring-0"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
};
