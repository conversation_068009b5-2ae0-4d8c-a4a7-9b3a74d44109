import { Button } from '../../ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '../../ui/card';
import { User, MapPin, Pencil } from 'lucide-react';
import { formatDate, capitalize } from '../utils/profileUtils';

export function PersonalDetailsTab({
	personalDetails,
	onEditSection,
	nationality,
}) {
	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<div className="flex justify-between items-center">
						<div>
							<CardTitle className="flex items-center gap-2">
								<User className="h-5 w-5 text-indigo-600" />
								Personal Information
							</CardTitle>
							<CardDescription>
								Your personal information. Click edit to request changes.
							</CardDescription>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEditSection('personal')}
							className="flex items-center self-start gap-2"
						>
							<Pencil className="h-4 w-4" />
							Edit Section
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-6">
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<div>
							<p className="text-sm text-muted-foreground">Full Name</p>
							<p className="font-medium">{personalDetails?.name || 'N/A'}</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Employee ID</p>
							<p className="font-medium">
								{personalDetails?.employeeOrgId || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Date of Birth</p>
							<p className="font-medium">{formatDate(personalDetails?.dob)}</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Age</p>
							<p className="font-medium">
								{personalDetails?.age ? `${personalDetails.age} years` : 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Gender</p>
							<p className="font-medium">
								{capitalize(personalDetails?.gender || 'N/A')}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">IC/FIN Number</p>
							<p className="font-medium">
								{personalDetails?.icFinNumber || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Religion</p>
							<p className="font-medium">
								{capitalize(personalDetails?.religion || 'N/A')}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Race</p>
							<p className="font-medium">
								{capitalize(personalDetails?.race || 'N/A')}
							</p>
						</div>
					</div>
				</CardContent>
			</Card>

			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<CardTitle className="flex items-center gap-2">
						<MapPin className="h-5 w-5 text-indigo-600" />
						Address & Citizenship
					</CardTitle>
					<CardDescription>
						Address and citizenship details cannot be edited. Please contact HR
						for any corrections.
					</CardDescription>
				</CardHeader>
				<CardContent className="pt-6">
					<div className="mb-6">
						<p className="text-sm text-muted-foreground">Address</p>
						<p className="font-medium whitespace-pre-line">
							{personalDetails?.address || 'N/A'}
						</p>
					</div>

					<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<div>
							<p className="text-sm text-muted-foreground">Postal Code</p>
							<p className="font-medium">
								{personalDetails?.postalCode || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Street Name</p>
							<p className="font-medium">
								{personalDetails?.streetName || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Nationality</p>
							<p className="font-medium">{capitalize(nationality)}</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">
								Residential Status
							</p>
							<p className="font-medium">
								{personalDetails?.residentialStatus || 'N/A'}
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
