import axios from 'axios';
import { clsx } from 'clsx';
import { toast } from 'sonner';
import { twMerge } from 'tailwind-merge';
import { format, formatRelative, parseISO } from 'date-fns';

export function cn(...inputs) {
	return twMerge(clsx(inputs));
}

export const userRoles = {
	SUPER_ADMIN: 1,
	COUNTRY_ADMIN: 2,
	GL<PERSON><PERSON>IED_CLIENT_ADMIN: 3,
	CLIENT_ADMIN: 4,
	BUSINESS_ADMIN: 5,
	DEPARTMENT_ADMIN: 6,
	MODULE_ADMIN: 7,
	REPORTING_MANAGER: 8,
	APPROVER: 9,
	EMPLOYEE: 10,
};

export const qualificationTypes = {
	UNDER_GRADUATE: 'UNDER_GRADUATE',
	GRADUATE: 'GRADUATE',
	NO_FORMAL_EDUCATION: 'NO_FORMAL_EDUCATION',
	POST_GRADUATE: 'POST_GRADUATE',
};

export const weeksInMonth = 4.33;

export const customFetch = axios.create({
	baseURL: 'http://localhost:5000/api/v1', // localhost
	// baseURL: 'https://tms-backend-muzr.onrender.com/api/v1', // render stagings
	// baseURL: 'https://harp-hr-backend.onrender.com/api/v1', // render production

	withCredentials: true,
});

export const getInitials = (name) => {
	if (!name) return '';
	return name
		.split(' ')
		.map((n) => n[0])
		.join('')
		.toUpperCase();
};

export const showErrors = (errorResponse) => {
	if (errorResponse === undefined || errorResponse === null) {
		return toast.error({
			title: 'Application Error',
			description: 'Something went wrong. Please try again later.',
		});
	}

	if (!errorResponse && !errorResponse?.status) {
		return toast.error(
			'Network error or CORS issue. Please check your connection or try again later.'
		);
	}

	// Handle 404 Not Found
	if (errorResponse.status === 404) {
		return toast.error('Requested resource was not found (404).');
	}

	// Handle 429 Too Many Requests
	if (errorResponse.status === 429) {
		return toast.error(
			'Too many requests. Please slow down and try again shortly.'
		);
	}

	if (!errorResponse?.error?.errors) {
		if (!errorResponse?.message) {
			return toast.error('Something went wrong. Please try again later.');
		}
		return toast.error(errorResponse.message);
	}

	const { message, error } = errorResponse;
	const errors = error.errors;

	let errorMessages = [];

	// Collect general errors
	if (errors._errors) {
		errorMessages.push(...errors._errors);
	}

	// Collect field-specific errors
	Object.entries(errors).forEach(([field, errorObj]) => {
		if (field !== '_errors' && errorObj._errors) {
			errorObj._errors.forEach((msg) => {
				errorMessages.push(`${field}: ${msg}`);
			});
		}
	});

	// Display errors inside an ordered list
	toast.error(message, {
		description: errorMessages
			.map((msg, idx) => `${idx + 1}. ${msg}`)
			.join('\n'),
		duration: 7000, // Display for 7 seconds
	});
};

export const formatDate = (dateString) => {
	const date = new Date(dateString);
	return new Intl.DateTimeFormat('en-US', {
		year: 'numeric',
		month: 'short',
		day: 'numeric',
	}).format(date);
};

export const calculateAge = (dob) => {
	const today = new Date();
	const birthDate = new Date(dob);
	let age = today.getFullYear() - birthDate.getFullYear();
	const monthDiff = today.getMonth() - birthDate.getMonth();
	if (
		monthDiff < 0 ||
		(monthDiff === 0 && today.getDate() < birthDate.getDate())
	) {
		age--;
	}
	return age;
};

export const getICFinPrefixes = (watchResidentialStatus) => {
	switch (watchResidentialStatus) {
		case 'Singapore Citizen':
		case 'Singapore PR':
			return ['S', 'T'];
		case 'Employment Pass':
		case 'SPass':
		case 'Work Permit':
		case 'LOC':
			return ['F', 'G', 'M'];
		default:
			return [];
	}
};

export const formatBreadcrumb = (text) => {
	return text.replace(/-/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());
};

export const generateRandomSixCharCode = () => {
	const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
	const code = Array.from({ length: 6 }, () =>
		charset.charAt(Math.floor(Math.random() * charset.length))
	).join('');
	return code;
};

export const zodResolver = (schema) => async (values) => {
	try {
		await schema.parseAsync(values);
		return { values, errors: {} };
	} catch (error) {
		if (error.errors) {
			return {
				values: {},
				errors: error.errors.reduce((acc, curr) => {
					acc[curr.path.join('.')] = {
						type: curr.code,
						message: curr.message,
					};
					return acc;
				}, {}),
			};
		}
		return { values: {}, errors: {} }; // Should not happen with Zod
	}
};

export function formatRelativeDate(dateString) {
	if (!dateString) return 'N/A';
	try {
		const date =
			typeof dateString === 'string'
				? parseISO(dateString)
				: new Date(dateString);
		return formatRelative(date, new Date());
	} catch (error) {
		console.error('Error formatting relative date:', dateString, error);
		return 'Invalid Date';
	}
}

export const countryDataFormats = [
	{
		country: 'Singapore',
		registration: {
			label: 'UEN',
			description:
				'9–10 alphanumeric, various formats (e.g., 8 digits + letter, year + digits + letter)',
			//FIXME: 10 alphanumeric and use the YYYYnnnnnA format
			regex: /^(?:\d{8}[A-Z]|\d{4}\d{4}[A-Z]|[A-Z]\d{2}[A-Z]{2}\d{4}[A-Z])$/i,
		},
		employeeIdTypes: ['NRIC', 'FIN'],
		foreignerStatus: 'Employment Pass/SPass/Work Permit',
		additionalPayrollInputs: ['PR Status', 'CPF Applicability'],
	},
	{
		country: 'Malaysia',
		registration: {
			label: 'SSM',
			description: '12-digit: YYYY + entity type (01–06) + 6-digit sequence',
			regex: /^[0-9]{4}(?:01|02|03|04|05|06)[0-9]{6}$/,
		},
		employeeIdTypes: ['MyKad', 'Passport Number'],
		foreignerStatus: 'Employment Pass/Visit Pass/Work Permit',
		additionalPayrollInputs: [
			'EPF Number',
			'SOCSO Number',
			'Income Tax Number',
		],
	},
	{
		country: 'Indonesia',
		registration: {
			label: 'NPWP',
			description: '15 digits',
			regex: /^[0-9]{15}$/,
		},
		employeeIdTypes: ['NIK'],
		foreignerStatus: 'KITAS/KITAP',
		additionalPayrollInputs: ['BPJS Number', 'NPWP (Tax ID)'],
	},
	{
		country: 'Philippines',
		registration: {
			label: 'SEC',
			description: 'e.g., CS20YY-##### or older: CN/A + digits',
			regex: /^(?:CS20\d{2}-\d{5}|CN\d{9}|C[A-Z]\d{7})$/,
		},
		employeeIdTypes: ['PhilSys ID', 'TIN'],
		foreignerStatus: 'AEP, Special Work Permit',
		additionalPayrollInputs: ['SSS Number', 'PhilHealth', 'Pag-IBIG'],
	},
	{
		country: 'India',
		registration: {
			label: 'CIN',
			description:
				'21 alphanumeric: U/L + 5 digits + 2 letters + 4 digits + 3 letters + 6 digits',
			regex: /^[UL][0-9]{5}[A-Za-z]{2}[0-9]{4}[A-Za-z]{3}[0-9]{6}$/,
		},
		employeeIdTypes: ['PAN', 'Aadhaar'],
		foreignerStatus: 'Employment Visa',
		additionalPayrollInputs: ['EPF/UAN', 'Professional Tax', 'ESI'],
	},
	{
		country: 'Australia',
		registration: {
			label: 'ABN',
			description: '11 digits',
			regex: /^[0-9]{11}$/,
		},
		employeeIdTypes: ['TFN'],
		foreignerStatus: 'Visa Subclass (e.g., 482, 186)',
		additionalPayrollInputs: ['Superannuation Fund', 'VEVO Status'],
	},
	{
		country: 'United Kingdom',
		registration: {
			label: 'CRN',
			description: '8 digits or 2 letters + 6 digits',
			regex: /^(?:[0-9]{8}|[A-Za-z]{2}[0-9]{6})$/,
		},
		employeeIdTypes: ['National Insurance Number'],
		foreignerStatus: 'Visa Type (Skilled Worker, etc.)',
		additionalPayrollInputs: ['NI Number Proof', 'Residency Status'],
	},
	{
		country: 'France',
		registration: {
			label: 'SIRET',
			description: '14-digit SIREN + NIC',
			regex: /^[0-9]{14}$/,
		},
		employeeIdTypes: ['Numéro de Sécurité Sociale'],
		foreignerStatus: 'Titre de séjour',
		additionalPayrollInputs: ['Numéro fiscal', 'CAF (if applicable)'],
	},
	{
		country: 'Germany',
		registration: {
			label: 'Handelsregisternummer',
			description: 'HRB or HRA + 5–6 digits',
			regex: /^(?:HRB|HRA)[0-9]{5,6}$/,
		},
		employeeIdTypes: ['Steuer‑ID', 'Sozialversicherungsnummer'],
		foreignerStatus: 'Residence Permit/Blue Card',
		additionalPayrollInputs: ['Health Insurance Info', 'Tax Class'],
	},
	{
		country: 'Switzerland',
		registration: {
			label: 'UID',
			description: 'CHE-nnn.nnn.nnn',
			regex: /^CHE-[0-9]{3}\.[0-9]{3}\.[0-9]{3}$/,
		},
		employeeIdTypes: ['AHV Number'],
		foreignerStatus: 'Permit L/B/C',
		additionalPayrollInputs: [
			'Canton',
			'LPP (Pension)',
			'Residence Permit Type',
		],
	},
	{
		country: 'US Global',
		registration: {
			label: 'Business Registration Number',
			description: 'Varies by state',
			regex: /^.*$/, // any format
		},
		employeeIdTypes: ['Identification Number'],
		foreignerStatus: 'Citizen/Others',
		additionalPayrollInputs: [], // none standardized
	},
];
