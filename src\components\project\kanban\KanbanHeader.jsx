'use client';

import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Users,
	Settings,
	Filter,
	MoreHorizontal,
	ArrowLeft,
	Calendar,
	BarChart3,
	Share2,
	Eye,
	EyeOff,
	ChevronDown,
	ChevronUp,
	Sparkles,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { SimpleLoader } from '@/components/loading-component';

/**
 * KanbanHeader Component
 * Header for the kanban board with project info and controls
 */
export const KanbanHeader = ({
	onBackClick,
	onSettingsClick,
	onFilterClick,
	onMembersClick,
	onMenuClick,
	onShareClick,
	onCalendarClick,
	onAnalyticsClick,
	onGlassModeToggle,
	isGlassMode = false,
	className,
	...props
}) => {
	const [isCollapsed, setIsCollapsed] = useState(true);
	const { projectDetails: project } = useAppSelector((store) => store.projects);
	const { taskBoard, isLoading } = useAppSelector((store) => store.tasks);

	if (!project) return null;

	// console.log(project, taskBoard);

	// Calculate project statistics from kanban data
	// const totalCards =
	// 	taskBoard?.groups.reduce((total, group) => total + group.tasks.length, 0) ||
	// 	0;
	// const completedCards =
	// 	taskBoard?.groups.find((group) => group._id === 'done')?.tasks.length || 0;
	const totalCards = 3;
	const completedCards = 1;
	const progressPercentage =
		totalCards > 0 ? Math.round((completedCards / totalCards) * 100) : 0;

	const getStatusColor = (status) => {
		const colors = {
			active: 'bg-green-100 text-green-800',
			planning: 'bg-blue-100 text-blue-800',
			completed: 'bg-gray-100 text-gray-800',
			on_hold: 'bg-yellow-100 text-yellow-800',
		};
		return colors[status] || colors.active;
	};

	const getPriorityColor = (priority) => {
		const colors = {
			high: 'bg-red-100 text-red-800',
			medium: 'bg-yellow-100 text-yellow-800',
			low: 'bg-green-100 text-green-800',
		};
		return colors[priority] || colors.medium;
	};

	// if (isLoading) {
	// 	return (
	// 		<div className="min-h-screen items-center justify-center">
	// 			<SimpleLoader />
	// 		</div>
	// 	);
	// }
	return (
		<div
			className={cn(
				// Conditional styling based on glass mode
				isGlassMode
					? 'bg-white/10 backdrop-blur-md border border-white/20'
					: 'bg-gray-100 dark:bg-background border border-gray-200 dark:border-neutral-700',
				'p-4',
				'shadow-sm',
				'transition-all duration-300',
				className
			)}
			{...props}
		>
			<div className="flex items-center justify-between">
				{/* Left Section - Back button and Project Info */}
				<div className="flex items-center gap-4">
					<Button
						variant="ghost"
						size="sm"
						onClick={onBackClick}
						className={cn(
							isGlassMode
								? 'text-white/70 hover:text-white hover:bg-white/20'
								: 'text-gray-700 hover:text-gray-900 hover:bg-gray-100',
							'p-2 transition-colors duration-300'
						)}
					>
						<ArrowLeft className="h-4 w-4" />
					</Button>

					<div className="flex items-center gap-3">
						{/* Project Title */}
						<h1
							className={cn(
								'text-xl font-bold transition-colors duration-300 dark:text-white',
								isGlassMode ? 'text-white' : 'text-gray-900'
							)}
						>
							{project?.details?.name}
						</h1>

						{/* Status Badge */}
						<Badge
							className={cn(
								'capitalize text-xs',
								getStatusColor(project?.details?.status)
							)}
						>
							{project?.details?.status.replace('_', ' ')}
						</Badge>

						{/* Priority Badge */}
						{/* <Badge
							className={cn(
								'capitalize text-xs',
								getPriorityColor(project.priority)
							)}
						>
							{project.priority}
						</Badge> */}
					</div>
				</div>

				{/* Right Section - Controls and Members */}
				<div className="flex items-center gap-3">
					{/* Project Members */}
					{project?.assignedEmployees &&
						project?.assignedEmployees.length > 0 && (
							<div className="flex items-center gap-2">
								<div className="flex -space-x-1">
									{project?.assignedEmployees
										.slice(0, 4)
										.map((member, index) => (
											<Avatar
												key={member?.userId}
												src={member?.profilePhoto}
												className={cn(
													'w-8 h-8 border-2 ring-1 transition-colors duration-300',
													isGlassMode
														? 'border-white ring-white/20'
														: 'border-gray-200 ring-gray-200'
												)}
												style={{
													zIndex: project?.assignedEmployees?.length - index,
												}}
											>
												<AvatarImage
													src={member.profilePhoto || '/placeholder.svg'}
													alt={member.name || 'Profile'}
												/>
												<AvatarFallback
													className={cn(
														'text-xs bg-white/20 text-gray-500',
														isGlassMode && 'text-white'
													)}
												>
													{member.name
														.split(' ')
														.map((n) => n[0])
														.join('')}
												</AvatarFallback>
											</Avatar>
										))}

									{project?.assignedEmployees?.length > 4 && (
										<div
											className={cn(
												'w-8 h-8 rounded-full bg-white/20 border-2 border-white ring-1 ring-white/20 flex items-center justify-center text-xs font-medium',
												isGlassMode
													? 'border-white ring-white/20 text-white dark:text-white'
													: 'border-gray-200 ring-gray-200 text-gray-700 dark:text-white'
											)}
										>
											+{project?.assignedEmployees?.length - 4}
										</div>
									)}
								</div>

								{/* <Button
								variant="ghost"
								size="sm"
								onClick={onMembersClick}
								className={cn(
									isGlassMode
										? 'text-white/70 hover:text-white hover:bg-white/20'
										: 'text-gray-500 hover:text-gray-700 hover:bg-gray-100',
									'p-2 transition-colors duration-300'
								)}
							>
								<Users className="h-4 w-4" />
							</Button> */}
							</div>
						)}

					{/* Glass Mode Toggle Button */}
					<Button
						variant="ghost"
						size="sm"
						onClick={onGlassModeToggle}
						className={cn(
							isGlassMode
								? 'text-white/70 hover:text-white hover:bg-white/20'
								: 'text-gray-500 hover:text-gray-700 hover:bg-gray-100',
							'p-2 transition-colors duration-300'
						)}
						title={
							isGlassMode ? 'Switch to solid mode' : 'Switch to glass mode'
						}
					>
						<Sparkles
							className={cn(
								'h-4 w-4 transition-all duration-300 dark:text-foreground',
								isGlassMode && 'text-muted-foreground'
							)}
						/>
					</Button>

					{/* Collapse/Expand Button */}
					<Button
						variant="ghost"
						size="sm"
						onClick={() => setIsCollapsed(!isCollapsed)}
						className={cn(
							isGlassMode
								? 'text-white/70 hover:text-white hover:bg-white/20'
								: 'text-gray-500 hover:text-gray-700 hover:bg-gray-100',
							'p-2 transition-colors duration-300 dark:text-foreground'
						)}
					>
						{isCollapsed ? (
							<ChevronDown className="h-4 w-4" />
						) : (
							<ChevronUp className="h-4 w-4" />
						)}
					</Button>
				</div>
			</div>

			{/* Project Description and Progress */}
			{!isCollapsed && (
				<div
					className={cn(
						'mt-3 pt-3 transition-all duration-300',
						isGlassMode
							? 'border-t border-white/20'
							: 'border-t border-gray-200'
					)}
				>
					{project?.details?.description && (
						<p
							className={cn(
								'text-sm max-w-2xl mb-3 transition-colors duration-300 dark:text-foreground',
								isGlassMode ? 'text-white/80' : 'text-gray-600'
							)}
						>
							{project?.details?.description}
						</p>
					)}

					{/* Progress Bar */}
					{totalCards > 0 && (
						<div className="flex items-center gap-3">
							<div className="flex-1 max-w-xs">
								<div
									className={cn(
										'flex items-center justify-between text-xs mb-1 transition-colors duration-300 dark:text-foreground',
										isGlassMode ? 'text-white/80' : 'text-gray-600'
									)}
								>
									<span>Progress</span>
									<span>{progressPercentage}%</span>
								</div>
								<div
									className={cn(
										'w-full rounded-full h-2 transition-colors duration-300 dark:text-foreground',
										isGlassMode ? 'bg-white/20' : 'bg-gray-200'
									)}
								>
									<div
										className={cn(
											'h-2 rounded-full transition-all duration-300 dark:text-foreground',
											isGlassMode ? 'bg-white/80' : 'bg-blue-500'
										)}
										style={{ width: `${progressPercentage}%` }}
									/>
								</div>
							</div>
							<div
								className={cn(
									'text-xs transition-colors duration-300 dark:text-foreground',
									isGlassMode ? 'text-white/70' : 'text-gray-500'
								)}
							>
								{completedCards} of {totalCards} tasks completed
							</div>
						</div>
					)}
				</div>
			)}
		</div>
	);
};
