import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchSingleTaskDetails,
	updateTask,
	updateTaskCoverImage,
} from '@/lib/features/tasks/tasksSlice';

/**
 * Custom hook for managing task data and attachments
 * Handles data fetching and attachment management
 */
export const useTaskData = (taskId, taskForm, updateFormsWithTaskData) => {
	const dispatch = useAppDispatch();
	const { taskDetails: task, isLoading } = useAppSelector(
		(store) => store.tasks
	);

	// Fetch task details when taskId changes
	useEffect(() => {
		if (taskId) {
			dispatch(fetchSingleTaskDetails(taskId));
		}
	}, [dispatch, taskId]);

	// Update forms when task data is loaded
	useEffect(() => {
		if (task && updateFormsWithTaskData) {
			updateFormsWithTaskData(task);
		}
	}, [task, updateFormsWithTaskData]);

	// Helper functions for cover image management
	const getImageAttachments = useCallback(() => {
		return task?.media?.filter((att) => att.resource_type === 'image');
	}, [task]);

	const getCoverImage = useCallback(() => {
		const coverImageUrl = taskForm.watch('coverImage');
		if (!coverImageUrl) return null;
		return getImageAttachments().find((att) => att.url === coverImageUrl);
	}, [taskForm, getImageAttachments]);

	const handleRemoveCoverImage = useCallback(() => {
		taskForm.setValue('coverImage', '');
		if (task?._id) {
			dispatch(
				updateTaskCoverImage({
					projectId: task.projectId,
					taskData: { coverImage: '', taskId: task._id },
				})
			);
		}
	}, [taskForm, dispatch, task]);

	const handleSetCoverImage = useCallback(
		(attachmentUrl) => {
			taskForm.setValue('coverImage', attachmentUrl);
			if (task?._id) {
				dispatch(
					updateTaskCoverImage({
						projectId: task.projectId,
						taskData: { coverImage: attachmentUrl, taskId: task._id },
					})
				);
			}
		},
		[taskForm, dispatch, task]
	);

	return {
		task,
		isLoading,
		getImageAttachments,
		getCoverImage,
		handleRemoveCoverImage,
		handleSetCoverImage,
	};
};
