import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '../../ui/card';
import { Heart, Baby, Plus, Pencil } from 'lucide-react';
import { formatDate, capitalize } from '../utils/profileUtils';

export function FamilyTab({ family, onEditSection, onAddChild }) {
	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<div className="flex justify-between items-center">
						<div>
							<CardTitle className="flex items-center gap-2">
								<Heart className="h-5 w-5 text-rose-500" />
								Marital Status
							</CardTitle>
							<CardDescription>
								Your family information. Click edit to request changes.
							</CardDescription>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEditSection('family')}
							className="flex items-center self-start gap-2"
						>
							<Pencil className="h-4 w-4" />
							Edit Section
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-6">
					<div className="grid gap-4">
						<div>
							<p className="text-sm text-muted-foreground">Marital Status</p>
							<p className="font-medium">
								{capitalize(family?.maritalStatus || 'N/A')}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Spouse Name</p>
							<p className="font-medium">{family?.spouseName || 'N/A'}</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">
								Spouse Employment Status
							</p>
							<p className="font-medium">
								{(family?.spouseEmploymentStatus &&
									capitalize(family?.spouseEmploymentStatus)) ||
									'N/A'}
							</p>
						</div>
					</div>
				</CardContent>
			</Card>

			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<div className="flex justify-between items-center">
						<div>
							<CardTitle className="flex items-center gap-2">
								<Baby className="h-5 w-5 text-blue-500" />
								Children
							</CardTitle>
							<CardDescription>
								Manage your family information including marital status, spouse
								details, and children.
							</CardDescription>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={onAddChild}
							className="flex items-center self-start gap-2"
						>
							<Plus className="h-4 w-4" />
							Add Child
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-6">
					{family?.children && family.children.length > 0 ? (
						<div className="space-y-4">
							{family.children.map((child, index) => (
								<div key={index} className="p-4 rounded-lg border bg-slate-50">
									<div className="flex justify-between items-start">
										<div>
											<h4 className="font-semibold">{child.name}</h4>
											<p className="text-sm text-muted-foreground">
												Age: {child.age} years
											</p>
											<p className="text-sm text-muted-foreground mt-1">
												Nationality: {capitalize(child.nationality)}
											</p>
										</div>
										<Badge className="bg-blue-100 text-blue-800">
											DOB: {formatDate(child.dob)}
										</Badge>
									</div>
								</div>
							))}
						</div>
					) : (
						<p className="text-muted-foreground">
							No children information available.
						</p>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
